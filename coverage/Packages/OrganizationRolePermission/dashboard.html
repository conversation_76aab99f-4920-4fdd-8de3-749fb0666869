<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Packages/OrganizationRolePermission</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Packages</a></li>
         <li class="breadcrumb-item"><a href="index.html">OrganizationRolePermission</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Exceptions/PermissionDoesNotExist.php.html#7">App\Packages\OrganizationRolePermission\Exceptions\PermissionDoesNotExist</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/HasPermission.php.html#10">App\Packages\OrganizationRolePermission\Traits\HasPermission</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#14">App\Packages\OrganizationRolePermission\OrganizationManager</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#18">App\Packages\OrganizationRolePermission\OrganizationUserManager</a></td><td class="text-right">6%</td></tr>
       <tr><td><a href="OrganizationServiceProvider.php.html#9">App\Packages\OrganizationRolePermission\OrganizationServiceProvider</a></td><td class="text-right">44%</td></tr>
       <tr><td><a href="Traits/RefreshesOrganizationCache.php.html#14">App\Packages\OrganizationRolePermission\Traits\RefreshesOrganizationCache</a></td><td class="text-right">45%</td></tr>
       <tr><td><a href="Traits/RefreshesOrganizationUserCache.php.html#11">App\Packages\OrganizationRolePermission\Traits\RefreshesOrganizationUserCache</a></td><td class="text-right">85%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="OrganizationManager.php.html#14">App\Packages\OrganizationRolePermission\OrganizationManager</a></td><td class="text-right">2698</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#18">App\Packages\OrganizationRolePermission\OrganizationUserManager</a></td><td class="text-right">2180</td></tr>
       <tr><td><a href="Traits/HasPermission.php.html#10">App\Packages\OrganizationRolePermission\Traits\HasPermission</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Traits/RefreshesOrganizationCache.php.html#14">App\Packages\OrganizationRolePermission\Traits\RefreshesOrganizationCache</a></td><td class="text-right">9</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Exceptions/PermissionDoesNotExist.php.html#9"><abbr title="App\Packages\OrganizationRolePermission\Exceptions\PermissionDoesNotExist::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#71"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getAlias">getAlias</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#79"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getCachedData">getCachedData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#87"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::loadFromCachedData">loadFromCachedData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#101"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::tryLoadFromCache">tryLoadFromCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#130"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::loadOrganizationRolePermissions">loadOrganizationRolePermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#179"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getFeatures">getFeatures</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#192"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getPermissions">getPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#205"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getRoles">getRoles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#215"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#228"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getOrganization">getOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#238"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::clearCacheCollection">clearCacheCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#262"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::checkOrganizationEnabledRolePermission">checkOrganizationEnabledRolePermission</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#285"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::loadOrganizationFeatures">loadOrganizationFeatures</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#323"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::filterPermissions">filterPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#346"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::filterRoles">filterRoles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#365"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getCacheKeyExpiry">getCacheKeyExpiry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#373"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::aliasModelFields">aliasModelFields</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#393"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::aliasedArray">aliasedArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#403"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getSerializedRoleRelation">getSerializedRoleRelation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#430"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getHydratedPermissionCollection">getHydratedPermissionCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#446"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getHydratedRoleCollection">getHydratedRoleCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationManager.php.html#455"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::hydrateRolesCache">hydrateRolesCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#59"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::setUser">setUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#75"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::registerPermissions">registerPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#86"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::loadOrganizationUserRolePermissions">loadOrganizationUserRolePermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#145"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getPermissions">getPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#158"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getRoles">getRoles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#180"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::forgotCacheForUsers">forgotCacheForUsers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#187"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getCacheKeyExpiry">getCacheKeyExpiry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#195"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#211"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::clearCacheCollection">clearCacheCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#233"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::checkOrganizationEnabledRolePermission">checkOrganizationEnabledRolePermission</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#260"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::filterPermissions">filterPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#283"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::filterRoles">filterRoles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#305"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::aliasModelFields">aliasModelFields</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#325"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::aliasedArray">aliasedArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#335"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getSerializedRoleRelation">getSerializedRoleRelation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#360"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getHydratedRolePermissionCollection">getHydratedRolePermissionCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#375"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getHydratedPermissionCollection">getHydratedPermissionCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#380"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::hydratePermissionCache">hydratePermissionCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/HasPermission.php.html#15"><abbr title="App\Packages\OrganizationRolePermission\Traits\HasPermission::checkPermissionTo">checkPermissionTo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/HasPermission.php.html#29"><abbr title="App\Packages\OrganizationRolePermission\Traits\HasPermission::hasPermissionTo">hasPermissionTo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/HasPermission.php.html#40"><abbr title="App\Packages\OrganizationRolePermission\Traits\HasPermission::filterPermission">filterPermission</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationServiceProvider.php.html#15"><abbr title="App\Packages\OrganizationRolePermission\OrganizationServiceProvider::boot">boot</abbr></a></td><td class="text-right">44%</td></tr>
       <tr><td><a href="Traits/RefreshesOrganizationCache.php.html#16"><abbr title="App\Packages\OrganizationRolePermission\Traits\RefreshesOrganizationCache::bootRefreshesOrganizationCache">bootRefreshesOrganizationCache</abbr></a></td><td class="text-right">45%</td></tr>
       <tr><td><a href="Traits/RefreshesOrganizationUserCache.php.html#13"><abbr title="App\Packages\OrganizationRolePermission\Traits\RefreshesOrganizationUserCache::bootRefreshesOrganizationUserCache">bootRefreshesOrganizationUserCache</abbr></a></td><td class="text-right">85%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="OrganizationUserManager.php.html#86"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::loadOrganizationUserRolePermissions">loadOrganizationUserRolePermissions</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OrganizationManager.php.html#130"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::loadOrganizationRolePermissions">loadOrganizationRolePermissions</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Traits/HasPermission.php.html#40"><abbr title="App\Packages\OrganizationRolePermission\Traits\HasPermission::filterPermission">filterPermission</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OrganizationManager.php.html#101"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::tryLoadFromCache">tryLoadFromCache</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OrganizationManager.php.html#403"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getSerializedRoleRelation">getSerializedRoleRelation</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OrganizationManager.php.html#262"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::checkOrganizationEnabledRolePermission">checkOrganizationEnabledRolePermission</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationManager.php.html#323"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::filterPermissions">filterPermissions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationManager.php.html#346"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::filterRoles">filterRoles</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationManager.php.html#373"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::aliasModelFields">aliasModelFields</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#233"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::checkOrganizationEnabledRolePermission">checkOrganizationEnabledRolePermission</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#260"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::filterPermissions">filterPermissions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#283"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::filterRoles">filterRoles</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#305"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::aliasModelFields">aliasModelFields</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#335"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getSerializedRoleRelation">getSerializedRoleRelation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrganizationManager.php.html#285"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::loadOrganizationFeatures">loadOrganizationFeatures</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#75"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::registerPermissions">registerPermissions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Traits/RefreshesOrganizationCache.php.html#16"><abbr title="App\Packages\OrganizationRolePermission\Traits\RefreshesOrganizationCache::bootRefreshesOrganizationCache">bootRefreshesOrganizationCache</abbr></a></td><td class="text-right">9</td></tr>
       <tr><td><a href="OrganizationManager.php.html#87"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::loadFromCachedData">loadFromCachedData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrganizationManager.php.html#393"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::aliasedArray">aliasedArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrganizationManager.php.html#446"><abbr title="App\Packages\OrganizationRolePermission\OrganizationManager::getHydratedRoleCollection">getHydratedRoleCollection</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#59"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::setUser">setUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#180"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::forgotCacheForUsers">forgotCacheForUsers</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#325"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::aliasedArray">aliasedArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#375"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::getHydratedPermissionCollection">getHydratedPermissionCollection</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrganizationUserManager.php.html#380"><abbr title="App\Packages\OrganizationRolePermission\OrganizationUserManager::hydratePermissionCache">hydratePermissionCache</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/HasPermission.php.html#15"><abbr title="App\Packages\OrganizationRolePermission\Traits\HasPermission::checkPermissionTo">checkPermissionTo</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:24:01 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([2,2,0,0,0,2,0,0,0,1,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([44,0,0,0,0,2,0,0,0,1,0,7], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[100,0,"<a href=\"Exceptions\/OrganizationNotFound.php.html#7\">App\\Packages\\OrganizationRolePermission\\Exceptions\\OrganizationNotFound<\/a>"],[0,1,"<a href=\"Exceptions\/PermissionDoesNotExist.php.html#7\">App\\Packages\\OrganizationRolePermission\\Exceptions\\PermissionDoesNotExist<\/a>"],[3.205128205128205,54,"<a href=\"OrganizationManager.php.html#14\">App\\Packages\\OrganizationRolePermission\\OrganizationManager<\/a>"],[44.44444444444444,1,"<a href=\"OrganizationServiceProvider.php.html#9\">App\\Packages\\OrganizationRolePermission\\OrganizationServiceProvider<\/a>"],[6.451612903225806,51,"<a href=\"OrganizationUserManager.php.html#18\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager<\/a>"],[0,9,"<a href=\"Traits\/HasPermission.php.html#10\">App\\Packages\\OrganizationRolePermission\\Traits\\HasPermission<\/a>"],[45,5,"<a href=\"Traits\/RefreshesOrganizationCache.php.html#14\">App\\Packages\\OrganizationRolePermission\\Traits\\RefreshesOrganizationCache<\/a>"],[85.71428571428571,1,"<a href=\"Traits\/RefreshesOrganizationUserCache.php.html#11\">App\\Packages\\OrganizationRolePermission\\Traits\\RefreshesOrganizationUserCache<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Exceptions\/PermissionDoesNotExist.php.html#9\">App\\Packages\\OrganizationRolePermission\\Exceptions\\PermissionDoesNotExist::create<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#71\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getAlias<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#79\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getCachedData<\/a>"],[0,2,"<a href=\"OrganizationManager.php.html#87\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::loadFromCachedData<\/a>"],[0,5,"<a href=\"OrganizationManager.php.html#101\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::tryLoadFromCache<\/a>"],[0,6,"<a href=\"OrganizationManager.php.html#130\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::loadOrganizationRolePermissions<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#179\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getFeatures<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#192\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getPermissions<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#205\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getRoles<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#215\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::setOrganization<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#228\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getOrganization<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#238\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::clearCacheCollection<\/a>"],[100,1,"<a href=\"OrganizationManager.php.html#248\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::forgetCachedPermissions<\/a>"],[100,1,"<a href=\"OrganizationManager.php.html#257\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getCacheKey<\/a>"],[0,4,"<a href=\"OrganizationManager.php.html#262\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::checkOrganizationEnabledRolePermission<\/a>"],[0,3,"<a href=\"OrganizationManager.php.html#285\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::loadOrganizationFeatures<\/a>"],[0,4,"<a href=\"OrganizationManager.php.html#323\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::filterPermissions<\/a>"],[0,4,"<a href=\"OrganizationManager.php.html#346\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::filterRoles<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#365\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getCacheKeyExpiry<\/a>"],[0,4,"<a href=\"OrganizationManager.php.html#373\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::aliasModelFields<\/a>"],[0,2,"<a href=\"OrganizationManager.php.html#393\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::aliasedArray<\/a>"],[0,5,"<a href=\"OrganizationManager.php.html#403\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getSerializedRoleRelation<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#430\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getHydratedPermissionCollection<\/a>"],[0,2,"<a href=\"OrganizationManager.php.html#446\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::getHydratedRoleCollection<\/a>"],[0,1,"<a href=\"OrganizationManager.php.html#455\">App\\Packages\\OrganizationRolePermission\\OrganizationManager::hydrateRolesCache<\/a>"],[44.44444444444444,1,"<a href=\"OrganizationServiceProvider.php.html#15\">App\\Packages\\OrganizationRolePermission\\OrganizationServiceProvider::boot<\/a>"],[100,1,"<a href=\"OrganizationUserManager.php.html#51\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getUser<\/a>"],[0,2,"<a href=\"OrganizationUserManager.php.html#59\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::setUser<\/a>"],[0,3,"<a href=\"OrganizationUserManager.php.html#75\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::registerPermissions<\/a>"],[0,7,"<a href=\"OrganizationUserManager.php.html#86\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::loadOrganizationUserRolePermissions<\/a>"],[0,1,"<a href=\"OrganizationUserManager.php.html#145\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getPermissions<\/a>"],[0,1,"<a href=\"OrganizationUserManager.php.html#158\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getRoles<\/a>"],[100,1,"<a href=\"OrganizationUserManager.php.html#165\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getCacheKey<\/a>"],[100,1,"<a href=\"OrganizationUserManager.php.html#172\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getCachePrefix<\/a>"],[0,2,"<a href=\"OrganizationUserManager.php.html#180\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::forgotCacheForUsers<\/a>"],[0,1,"<a href=\"OrganizationUserManager.php.html#187\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getCacheKeyExpiry<\/a>"],[0,1,"<a href=\"OrganizationUserManager.php.html#195\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::setOrganization<\/a>"],[0,1,"<a href=\"OrganizationUserManager.php.html#211\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::clearCacheCollection<\/a>"],[100,1,"<a href=\"OrganizationUserManager.php.html#220\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::forgetCachedPermissions<\/a>"],[100,1,"<a href=\"OrganizationUserManager.php.html#228\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::forgotCacheByKey<\/a>"],[0,4,"<a href=\"OrganizationUserManager.php.html#233\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::checkOrganizationEnabledRolePermission<\/a>"],[0,4,"<a href=\"OrganizationUserManager.php.html#260\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::filterPermissions<\/a>"],[0,4,"<a href=\"OrganizationUserManager.php.html#283\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::filterRoles<\/a>"],[0,4,"<a href=\"OrganizationUserManager.php.html#305\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::aliasModelFields<\/a>"],[0,2,"<a href=\"OrganizationUserManager.php.html#325\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::aliasedArray<\/a>"],[0,4,"<a href=\"OrganizationUserManager.php.html#335\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getSerializedRoleRelation<\/a>"],[0,1,"<a href=\"OrganizationUserManager.php.html#360\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getHydratedRolePermissionCollection<\/a>"],[0,2,"<a href=\"OrganizationUserManager.php.html#375\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::getHydratedPermissionCollection<\/a>"],[0,2,"<a href=\"OrganizationUserManager.php.html#380\">App\\Packages\\OrganizationRolePermission\\OrganizationUserManager::hydratePermissionCache<\/a>"],[0,2,"<a href=\"Traits\/HasPermission.php.html#15\">App\\Packages\\OrganizationRolePermission\\Traits\\HasPermission::checkPermissionTo<\/a>"],[0,1,"<a href=\"Traits\/HasPermission.php.html#29\">App\\Packages\\OrganizationRolePermission\\Traits\\HasPermission::hasPermissionTo<\/a>"],[0,6,"<a href=\"Traits\/HasPermission.php.html#40\">App\\Packages\\OrganizationRolePermission\\Traits\\HasPermission::filterPermission<\/a>"],[45,5,"<a href=\"Traits\/RefreshesOrganizationCache.php.html#16\">App\\Packages\\OrganizationRolePermission\\Traits\\RefreshesOrganizationCache::bootRefreshesOrganizationCache<\/a>"],[85.71428571428571,1,"<a href=\"Traits\/RefreshesOrganizationUserCache.php.html#13\">App\\Packages\\OrganizationRolePermission\\Traits\\RefreshesOrganizationUserCache::bootRefreshesOrganizationUserCache<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
