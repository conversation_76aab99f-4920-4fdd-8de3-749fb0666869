<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Events/WorkOrder</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Events</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrder</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Actions/WorkOrderAccessInfoUpdated.php.html#15">App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderAwaitingAvailability.php.html#13">App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCanceled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderClaimPending.php.html#14">App\Events\WorkOrder\Actions\WorkOrderClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCreated.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderDueDateChanged.php.html#15">App\Events\WorkOrder\Actions\WorkOrderDueDateChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTESet.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTESet</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTEUpdated.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTEUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderPriorityChanged.php.html#17">App\Events\WorkOrder\Actions\WorkOrderPriorityChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderQualityCheck.php.html#14">App\Events\WorkOrder\Actions\WorkOrderQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderReadyToSchedule.php.html#14">App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderScheduled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderWorkInProgress.php.html#14">App\Events\WorkOrder\Actions\WorkOrderWorkInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#12">App\Events\WorkOrder\ActivityLog\BaseIssueEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/WorkOrderActivityLogCreated.php.html#14">App\Events\WorkOrder\ActivityLog\WorkOrderActivityLogCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLogEntry.php.html#13">App\Events\WorkOrder\ActivityLogEntry</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExternalWorkOrderUpdateEvent.php.html#10">App\Events\WorkOrder\ExternalWorkOrderUpdateEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueAssigned.php.html#14">App\Events\WorkOrder\Issue\WorkOrderIssueAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueBaseEvent.php.html#13">App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#13">App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteCreated.php.html#14">App\Events\WorkOrder\Note\WorkOrderNoteCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteDeleted.php.html#15">App\Events\WorkOrder\Note\WorkOrderNoteDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#14">App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#17">App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#19">App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#20">App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#20">App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#14">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripPausedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripResumedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#17">App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#15">App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#14">App\Events\WorkOrder\Quote\QuoteStateChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#14">App\Events\WorkOrder\ResidentAvailabilityBroadcast</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeleted.php.html#14">App\Events\WorkOrder\ResidentAvailabilityDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeletedEvent.php.html#14">App\Events\WorkOrder\ResidentAvailabilityDeletedEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#18">App\Events\WorkOrder\ResidentUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#10">App\Events\WorkOrder\Trip\TripBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripCanceled.php.html#10">App\Events\WorkOrder\Trip\TripCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteStart.php.html#11">App\Events\WorkOrder\Trip\TripEnRouteStart</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripReScheduled.php.html#10">App\Events\WorkOrder\Trip\TripReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduleInProgress.php.html#10">App\Events\WorkOrder\Trip\TripScheduleInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAddressUpdated.php.html#10">App\Events\WorkOrder\WorkOrderAddressUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeAdded.php.html#16">App\Events\WorkOrder\WorkOrderAssigneeAdded</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeRemoved.php.html#13">App\Events\WorkOrder\WorkOrderAssigneeRemoved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#16">App\Events\WorkOrder\WorkOrderAssigneeUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#11">App\Events\WorkOrder\WorkOrderBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDescriptionUpdated.php.html#10">App\Events\WorkOrder\WorkOrderDescriptionUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDetailsUpdate.php.html#13">App\Events\WorkOrder\WorkOrderDetailsUpdate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#22">App\Events\WorkOrder\WorkOrderHealthScoreChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListDeleted.php.html#15">App\Events\WorkOrder\WorkOrderListDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPaused.php.html#10">App\Events\WorkOrder\WorkOrderPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#16">App\Events\WorkOrder\WorkOrderPropertyAddressUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReOpen.php.html#10">App\Events\WorkOrder\WorkOrderReOpen</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReScheduled.php.html#10">App\Events\WorkOrder\WorkOrderReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResidentUpdated.php.html#10">App\Events\WorkOrder\WorkOrderResidentUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResolve.php.html#10">App\Events\WorkOrder\WorkOrderResolve</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderSendToVendor.php.html#11">App\Events\WorkOrder\WorkOrderSendToVendor</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallAssigned.php.html#16">App\Events\WorkOrder\WorkOrderServiceCallAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallRescheduled.php.html#16">App\Events\WorkOrder\WorkOrderServiceCallRescheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#16">App\Events\WorkOrder\WorkOrderStateChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#16">App\Events\WorkOrder\WorkOrderTagUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#13">App\Events\WorkOrder\WorkOrderUpdate</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderStateChange.php.html#16">App\Events\WorkOrder\WorkOrderStateChange</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#12">App\Events\WorkOrder\ActivityLog\BaseIssueEvent</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#17">App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#14">App\Events\WorkOrder\Quote\QuoteStateChange</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#13">App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#10">App\Events\WorkOrder\Trip\TripBaseEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#11">App\Events\WorkOrder\WorkOrderBaseEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Actions/WorkOrderAccessInfoUpdated.php.html#15">App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#15">App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#16">App\Events\WorkOrder\WorkOrderAssigneeUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#22">App\Events\WorkOrder\WorkOrderHealthScoreChange</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#16">App\Events\WorkOrder\WorkOrderTagUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#13">App\Events\WorkOrder\WorkOrderUpdate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Actions/WorkOrderNTESet.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTESet</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Actions/WorkOrderNTEUpdated.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTEUpdated</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripPausedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripResumedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Actions/WorkOrderCanceled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCanceled</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderClaimPending.php.html#14">App\Events\WorkOrder\Actions\WorkOrderClaimPending</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderCreated.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCreated</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderDueDateChanged.php.html#15">App\Events\WorkOrder\Actions\WorkOrderDueDateChanged</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderPriorityChanged.php.html#17">App\Events\WorkOrder\Actions\WorkOrderPriorityChanged</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderQualityCheck.php.html#14">App\Events\WorkOrder\Actions\WorkOrderQualityCheck</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderReadyToSchedule.php.html#14">App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderScheduled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderScheduled</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderWorkInProgress.php.html#14">App\Events\WorkOrder\Actions\WorkOrderWorkInProgress</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Note/WorkOrderNoteDeleted.php.html#15">App\Events\WorkOrder\Note\WorkOrderNoteDeleted</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#14">App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Actions/WorkOrderAccessInfoUpdated.php.html#22"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderAccessInfoUpdated.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderAccessInfoUpdated.php.html#54"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderAwaitingAvailability.php.html#20"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderAwaitingAvailability.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCanceled.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCanceled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCanceled.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderClaimPending.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderClaimPending.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderClaimPending.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCreated.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCreated.php.html#31"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderCreated.php.html#50"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderDueDateChanged.php.html#22"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderDueDateChanged.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderDueDateChanged.php.html#49"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTESet.php.html#23"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTESet.php.html#33"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTESet.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTESet.php.html#64"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTEUpdated.php.html#23"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTEUpdated.php.html#33"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTEUpdated.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderNTEUpdated.php.html#64"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderPriorityChanged.php.html#24"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderPriorityChanged.php.html#32"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderPriorityChanged.php.html#51"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderQualityCheck.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderQualityCheck.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderQualityCheck.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderReadyToSchedule.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderReadyToSchedule.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderReadyToSchedule.php.html#49"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderScheduled.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderScheduled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderScheduled.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderWorkInProgress.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderWorkInProgress.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Actions/WorkOrderWorkInProgress.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#16"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#18"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#37"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#47"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#63"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/WorkOrderActivityLogCreated.php.html#21"><abbr title="App\Events\WorkOrder\ActivityLog\WorkOrderActivityLogCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLogEntry.php.html#22"><abbr title="App\Events\WorkOrder\ActivityLogEntry::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLogEntry.php.html#33"><abbr title="App\Events\WorkOrder\ActivityLogEntry::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLogEntry.php.html#43"><abbr title="App\Events\WorkOrder\ActivityLogEntry::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLogEntry.php.html#53"><abbr title="App\Events\WorkOrder\ActivityLogEntry::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExternalWorkOrderUpdateEvent.php.html#35"><abbr title="App\Events\WorkOrder\ExternalWorkOrderUpdateEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueAssigned.php.html#21"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueAssigned::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueBaseEvent.php.html#17"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::getWorkOrderIssueDetails">getWorkOrderIssueDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueBaseEvent.php.html#50"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueBaseEvent.php.html#60"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueBaseEvent.php.html#74"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/WorkOrderIssueBaseEvent.php.html#84"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#17"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#55"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#65"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#79"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#89"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteCreated.php.html#21"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteDeleted.php.html#22"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteDeleted.php.html#30"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteDeleted.php.html#56"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#21"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#26"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#53"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#64"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#39"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#37"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#45"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#24"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#32"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#40"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#36"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#34"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#21"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripPausedLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripPausedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripPausedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripResumedLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripResumedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripResumedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#17"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#22"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#50"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#20"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#24"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#32"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#24"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#30"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#35"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#22"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#36"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#21"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#27"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#37"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#50"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#62"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#85"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#122"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#18"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#29"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#39"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#49"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeleted.php.html#21"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeletedEvent.php.html#18"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeletedEvent.php.html#28"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeletedEvent.php.html#38"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeletedEvent.php.html#48"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#31"><abbr title="App\Events\WorkOrder\ResidentUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#42"><abbr title="App\Events\WorkOrder\ResidentUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#53"><abbr title="App\Events\WorkOrder\ResidentUpdated::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#69"><abbr title="App\Events\WorkOrder\ResidentUpdated::getResidentDetails">getResidentDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#86"><abbr title="App\Events\WorkOrder\ResidentUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#96"><abbr title="App\Events\WorkOrder\ResidentUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdated.php.html#110"><abbr title="App\Events\WorkOrder\ResidentUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::getTrip">getTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#40"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#50"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#62"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#72"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripCanceled.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteStart.php.html#18"><abbr title="App\Events\WorkOrder\Trip\TripEnRouteStart::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripReScheduled.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripReScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduleInProgress.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripScheduleInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAddressUpdated.php.html#14"><abbr title="App\Events\WorkOrder\WorkOrderAddressUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeAdded.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeAdded::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeRemoved.php.html#20"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeRemoved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#32"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#42"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#52"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#18"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#82"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#92"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#104"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#114"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDescriptionUpdated.php.html#14"><abbr title="App\Events\WorkOrder\WorkOrderDescriptionUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDetailsUpdate.php.html#22"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDetailsUpdate.php.html#27"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDetailsUpdate.php.html#37"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDetailsUpdate.php.html#53"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDetailsUpdate.php.html#63"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#29"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#47"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#57"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#67"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#77"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListDeleted.php.html#24"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListDeleted.php.html#31"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListDeleted.php.html#41"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListDeleted.php.html#56"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListDeleted.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPaused.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#26"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#31"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#39"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::getPropertyDetails">getPropertyDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#47"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#57"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#69"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPropertyAddressUpdated.php.html#79"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReOpen.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderReOpen::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReScheduled.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderReScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResidentUpdated.php.html#14"><abbr title="App\Events\WorkOrder\WorkOrderResidentUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResolve.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderResolve::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderSendToVendor.php.html#18"><abbr title="App\Events\WorkOrder\WorkOrderSendToVendor::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderSendToVendor.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderSendToVendor::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderSendToVendor.php.html#28"><abbr title="App\Events\WorkOrder\WorkOrderSendToVendor::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallAssigned.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderServiceCallAssigned::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallRescheduled.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderServiceCallRescheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#29"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#40"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#53"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#63"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#112"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#149"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#25"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#36"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#46"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#56"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#22"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#30"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#40"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#55"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#65"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#50"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#32"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#36"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Actions/WorkOrderAccessInfoUpdated.php.html#54"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#63"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripPausedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PublicWebhook/Lula/TripResumedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#63"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Actions/WorkOrderAccessInfoUpdated.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderCanceled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderClaimPending.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderCreated.php.html#50"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderDueDateChanged.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderNTESet.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderNTEUpdated.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderPriorityChanged.php.html#32"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderQualityCheck.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderReadyToSchedule.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderScheduled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Actions/WorkOrderWorkInProgress.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#18"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Note/WorkOrderNoteBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Note/WorkOrderNoteDeleted.php.html#30"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#53"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#64"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#85"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quote/QuoteStateChange.php.html#122"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Trip/TripBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::getTrip">getTrip</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderAssigneeUpdated.php.html#52"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderBaseEvent.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderHealthScoreChange.php.html#29"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#112"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderStateChange.php.html#149"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderTagUpdated.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderUpdate.php.html#65"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:24:01 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([78,0,0,0,0,0,0,0,0,0,0,11], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([239,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"Actions\/WorkOrderAccessInfoUpdated.php.html#15\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderAwaitingAvailability.php.html#13\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderCanceled.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderClaimPending.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderCreated.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderDueDateChanged.php.html#15\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged<\/a>"],[0,5,"<a href=\"Actions\/WorkOrderNTESet.php.html#16\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet<\/a>"],[0,5,"<a href=\"Actions\/WorkOrderNTEUpdated.php.html#16\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderPriorityChanged.php.html#17\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderQualityCheck.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderReadyToSchedule.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderScheduled.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled<\/a>"],[0,4,"<a href=\"Actions\/WorkOrderWorkInProgress.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress<\/a>"],[0,8,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#12\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent<\/a>"],[0,1,"<a href=\"ActivityLog\/WorkOrderActivityLogCreated.php.html#14\">App\\Events\\WorkOrder\\ActivityLog\\WorkOrderActivityLogCreated<\/a>"],[0,4,"<a href=\"ActivityLogEntry.php.html#13\">App\\Events\\WorkOrder\\ActivityLogEntry<\/a>"],[0,1,"<a href=\"ExternalWorkOrderUpdateEvent.php.html#10\">App\\Events\\WorkOrder\\ExternalWorkOrderUpdateEvent<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueAssigned.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueAssigned<\/a>"],[0,6,"<a href=\"Issue\/WorkOrderIssueBaseEvent.php.html#13\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent<\/a>"],[100,0,"<a href=\"Issue\/WorkOrderIssueCanceled.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueCanceled<\/a>"],[100,0,"<a href=\"Issue\/WorkOrderIssueDeclined.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueDeclined<\/a>"],[100,0,"<a href=\"Issue\/WorkOrderIssueUpdated.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueUpdated<\/a>"],[0,7,"<a href=\"Note\/WorkOrderNoteBaseEvent.php.html#13\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteCreated.php.html#14\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteCreated<\/a>"],[0,4,"<a href=\"Note\/WorkOrderNoteDeleted.php.html#15\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted<\/a>"],[100,0,"<a href=\"Note\/WorkOrderNoteUpdated.php.html#12\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteUpdated<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook<\/a>"],[0,4,"<a href=\"PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#14\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook<\/a>"],[0,8,"<a href=\"PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#17\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#20\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#20\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#14\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook<\/a>"],[0,6,"<a href=\"PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook<\/a>"],[0,5,"<a href=\"PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook<\/a>"],[0,5,"<a href=\"PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook<\/a>"],[0,9,"<a href=\"PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#13\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook<\/a>"],[0,7,"<a href=\"PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#13\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#17\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook<\/a>"],[0,6,"<a href=\"PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#15\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook<\/a>"],[0,6,"<a href=\"PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook<\/a>"],[0,6,"<a href=\"PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook<\/a>"],[0,8,"<a href=\"Quote\/QuoteStateChange.php.html#14\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange<\/a>"],[0,4,"<a href=\"ResidentAvailabilityBroadcast.php.html#14\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeleted.php.html#14\">App\\Events\\WorkOrder\\ResidentAvailabilityDeleted<\/a>"],[0,4,"<a href=\"ResidentAvailabilityDeletedEvent.php.html#14\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent<\/a>"],[0,7,"<a href=\"ResidentUpdated.php.html#18\">App\\Events\\WorkOrder\\ResidentUpdated<\/a>"],[0,7,"<a href=\"Trip\/TripBaseEvent.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent<\/a>"],[0,1,"<a href=\"Trip\/TripCanceled.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripCanceled<\/a>"],[100,0,"<a href=\"Trip\/TripEnRoutePaused.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripEnRoutePaused<\/a>"],[100,0,"<a href=\"Trip\/TripEnRouteResumed.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripEnRouteResumed<\/a>"],[0,1,"<a href=\"Trip\/TripEnRouteStart.php.html#11\">App\\Events\\WorkOrder\\Trip\\TripEnRouteStart<\/a>"],[100,0,"<a href=\"Trip\/TripEnd.php.html#11\">App\\Events\\WorkOrder\\Trip\\TripEnd<\/a>"],[100,0,"<a href=\"Trip\/TripPaused.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripPaused<\/a>"],[0,1,"<a href=\"Trip\/TripReScheduled.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripReScheduled<\/a>"],[100,0,"<a href=\"Trip\/TripResume.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripResume<\/a>"],[0,1,"<a href=\"Trip\/TripScheduleInProgress.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripScheduleInProgress<\/a>"],[100,0,"<a href=\"Trip\/TripScheduled.php.html#11\">App\\Events\\WorkOrder\\Trip\\TripScheduled<\/a>"],[100,0,"<a href=\"Trip\/TripWorking.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripWorking<\/a>"],[0,1,"<a href=\"WorkOrderAddressUpdated.php.html#10\">App\\Events\\WorkOrder\\WorkOrderAddressUpdated<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeAdded.php.html#16\">App\\Events\\WorkOrder\\WorkOrderAssigneeAdded<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeRemoved.php.html#13\">App\\Events\\WorkOrder\\WorkOrderAssigneeRemoved<\/a>"],[0,6,"<a href=\"WorkOrderAssigneeUpdated.php.html#16\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated<\/a>"],[0,7,"<a href=\"WorkOrderBaseEvent.php.html#11\">App\\Events\\WorkOrder\\WorkOrderBaseEvent<\/a>"],[0,1,"<a href=\"WorkOrderDescriptionUpdated.php.html#10\">App\\Events\\WorkOrder\\WorkOrderDescriptionUpdated<\/a>"],[0,5,"<a href=\"WorkOrderDetailsUpdate.php.html#13\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate<\/a>"],[0,6,"<a href=\"WorkOrderHealthScoreChange.php.html#22\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange<\/a>"],[0,5,"<a href=\"WorkOrderListDeleted.php.html#15\">App\\Events\\WorkOrder\\WorkOrderListDeleted<\/a>"],[0,1,"<a href=\"WorkOrderPaused.php.html#10\">App\\Events\\WorkOrder\\WorkOrderPaused<\/a>"],[0,7,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#16\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated<\/a>"],[0,1,"<a href=\"WorkOrderReOpen.php.html#10\">App\\Events\\WorkOrder\\WorkOrderReOpen<\/a>"],[0,1,"<a href=\"WorkOrderReScheduled.php.html#10\">App\\Events\\WorkOrder\\WorkOrderReScheduled<\/a>"],[0,1,"<a href=\"WorkOrderResidentUpdated.php.html#10\">App\\Events\\WorkOrder\\WorkOrderResidentUpdated<\/a>"],[0,1,"<a href=\"WorkOrderResolve.php.html#10\">App\\Events\\WorkOrder\\WorkOrderResolve<\/a>"],[0,3,"<a href=\"WorkOrderSendToVendor.php.html#11\">App\\Events\\WorkOrder\\WorkOrderSendToVendor<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallAssigned.php.html#16\">App\\Events\\WorkOrder\\WorkOrderServiceCallAssigned<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallRescheduled.php.html#16\">App\\Events\\WorkOrder\\WorkOrderServiceCallRescheduled<\/a>"],[0,10,"<a href=\"WorkOrderStateChange.php.html#16\">App\\Events\\WorkOrder\\WorkOrderStateChange<\/a>"],[0,6,"<a href=\"WorkOrderTagUpdated.php.html#16\">App\\Events\\WorkOrder\\WorkOrderTagUpdated<\/a>"],[0,6,"<a href=\"WorkOrderUpdate.php.html#13\">App\\Events\\WorkOrder\\WorkOrderUpdate<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Actions\/WorkOrderAccessInfoUpdated.php.html#22\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderAccessInfoUpdated.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::getWorkOrderDetails<\/a>"],[0,3,"<a href=\"Actions\/WorkOrderAccessInfoUpdated.php.html#54\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderAwaitingAvailability.php.html#20\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability::broadcastAs<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderAwaitingAvailability.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderCanceled.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderCanceled.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderCanceled.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderClaimPending.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderClaimPending.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderClaimPending.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderCreated.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::broadcastAs<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderCreated.php.html#31\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::broadcastWith<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderCreated.php.html#50\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderDueDateChanged.php.html#22\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderDueDateChanged.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderDueDateChanged.php.html#49\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderNTESet.php.html#23\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastAs<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderNTESet.php.html#33\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastOn<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderNTESet.php.html#45\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderNTESet.php.html#64\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderNTEUpdated.php.html#23\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderNTEUpdated.php.html#33\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastOn<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderNTEUpdated.php.html#45\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderNTEUpdated.php.html#64\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderPriorityChanged.php.html#24\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderPriorityChanged.php.html#32\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderPriorityChanged.php.html#51\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderQualityCheck.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderQualityCheck.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderQualityCheck.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderReadyToSchedule.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderReadyToSchedule.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderReadyToSchedule.php.html#49\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderScheduled.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderScheduled.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderScheduled.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::broadcastWith<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderWorkInProgress.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::broadcastAs<\/a>"],[0,2,"<a href=\"Actions\/WorkOrderWorkInProgress.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"Actions\/WorkOrderWorkInProgress.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::broadcastWith<\/a>"],[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#16\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::__construct<\/a>"],[0,2,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#18\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::getWorkOrderActivityLog<\/a>"],[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#37\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#47\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::broadcastOn<\/a>"],[0,3,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#63\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::broadcastWith<\/a>"],[0,1,"<a href=\"ActivityLog\/WorkOrderActivityLogCreated.php.html#21\">App\\Events\\WorkOrder\\ActivityLog\\WorkOrderActivityLogCreated::broadcastAs<\/a>"],[0,1,"<a href=\"ActivityLogEntry.php.html#22\">App\\Events\\WorkOrder\\ActivityLogEntry::__construct<\/a>"],[0,1,"<a href=\"ActivityLogEntry.php.html#33\">App\\Events\\WorkOrder\\ActivityLogEntry::broadcastOn<\/a>"],[0,1,"<a href=\"ActivityLogEntry.php.html#43\">App\\Events\\WorkOrder\\ActivityLogEntry::broadcastAs<\/a>"],[0,1,"<a href=\"ActivityLogEntry.php.html#53\">App\\Events\\WorkOrder\\ActivityLogEntry::broadcastWith<\/a>"],[0,1,"<a href=\"ExternalWorkOrderUpdateEvent.php.html#35\">App\\Events\\WorkOrder\\ExternalWorkOrderUpdateEvent::__construct<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueAssigned.php.html#21\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueAssigned::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueBaseEvent.php.html#17\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::__construct<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueBaseEvent.php.html#22\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::getWorkOrderIssueDetails<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueBaseEvent.php.html#50\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueBaseEvent.php.html#60\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueBaseEvent.php.html#74\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/WorkOrderIssueBaseEvent.php.html#84\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteBaseEvent.php.html#17\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::__construct<\/a>"],[0,2,"<a href=\"Note\/WorkOrderNoteBaseEvent.php.html#22\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::getWorkOrderNoteDetails<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteBaseEvent.php.html#55\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteBaseEvent.php.html#65\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteBaseEvent.php.html#79\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteBaseEvent.php.html#89\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteCreated.php.html#21\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteCreated::broadcastAs<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteDeleted.php.html#22\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted::broadcastAs<\/a>"],[0,2,"<a href=\"Note\/WorkOrderNoteDeleted.php.html#30\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted::getWorkOrderNoteDetails<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteDeleted.php.html#56\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#21\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#26\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook::getWorkOrder<\/a>"],[0,2,"<a href=\"PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::__construct<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::fetchMedia<\/a>"],[0,2,"<a href=\"PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#53\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::getWorkOrder<\/a>"],[0,2,"<a href=\"PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#64\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#39\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#37\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#45\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#24\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#32\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#40\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#36\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#34\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#21\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook::getWorkOrder<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook::getWorkOrder<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#17\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#22\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook::getWorkOrder<\/a>"],[0,7,"<a href=\"PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#50\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#20\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#24\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook::getWorkOrder<\/a>"],[0,5,"<a href=\"PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#32\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#24\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#30\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#35\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#22\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#36\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#21\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"Quote\/QuoteStateChange.php.html#27\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::__construct<\/a>"],[0,1,"<a href=\"Quote\/QuoteStateChange.php.html#37\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::broadcastOn<\/a>"],[0,1,"<a href=\"Quote\/QuoteStateChange.php.html#50\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::broadcastAs<\/a>"],[0,1,"<a href=\"Quote\/QuoteStateChange.php.html#62\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::broadcastWith<\/a>"],[0,2,"<a href=\"Quote\/QuoteStateChange.php.html#85\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::getWorkOrder<\/a>"],[0,2,"<a href=\"Quote\/QuoteStateChange.php.html#122\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::getWorkOrderActivityLog<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#18\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::__construct<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#29\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::broadcastOn<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#39\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::broadcastAs<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#49\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::broadcastWith<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeleted.php.html#21\">App\\Events\\WorkOrder\\ResidentAvailabilityDeleted::__construct<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeletedEvent.php.html#18\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::__construct<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeletedEvent.php.html#28\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::broadcastOn<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeletedEvent.php.html#38\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::broadcastAs<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeletedEvent.php.html#48\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::broadcastWith<\/a>"],[0,1,"<a href=\"ResidentUpdated.php.html#31\">App\\Events\\WorkOrder\\ResidentUpdated::__construct<\/a>"],[0,1,"<a href=\"ResidentUpdated.php.html#42\">App\\Events\\WorkOrder\\ResidentUpdated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"ResidentUpdated.php.html#53\">App\\Events\\WorkOrder\\ResidentUpdated::getServiceRequestDetails<\/a>"],[0,1,"<a href=\"ResidentUpdated.php.html#69\">App\\Events\\WorkOrder\\ResidentUpdated::getResidentDetails<\/a>"],[0,1,"<a href=\"ResidentUpdated.php.html#86\">App\\Events\\WorkOrder\\ResidentUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ResidentUpdated.php.html#96\">App\\Events\\WorkOrder\\ResidentUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"ResidentUpdated.php.html#110\">App\\Events\\WorkOrder\\ResidentUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"Trip\/TripBaseEvent.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::__construct<\/a>"],[0,2,"<a href=\"Trip\/TripBaseEvent.php.html#22\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::getTrip<\/a>"],[0,1,"<a href=\"Trip\/TripBaseEvent.php.html#40\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"Trip\/TripBaseEvent.php.html#50\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"Trip\/TripBaseEvent.php.html#62\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"Trip\/TripBaseEvent.php.html#72\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"Trip\/TripCanceled.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripCanceled::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripEnRouteStart.php.html#18\">App\\Events\\WorkOrder\\Trip\\TripEnRouteStart::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripReScheduled.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripReScheduled::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripScheduleInProgress.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripScheduleInProgress::__construct<\/a>"],[0,1,"<a href=\"WorkOrderAddressUpdated.php.html#14\">App\\Events\\WorkOrder\\WorkOrderAddressUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeAdded.php.html#23\">App\\Events\\WorkOrder\\WorkOrderAssigneeAdded::__construct<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeRemoved.php.html#20\">App\\Events\\WorkOrder\\WorkOrderAssigneeRemoved::__construct<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeUpdated.php.html#23\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeUpdated.php.html#32\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeUpdated.php.html#42\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastOn<\/a>"],[0,2,"<a href=\"WorkOrderAssigneeUpdated.php.html#52\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeUpdated.php.html#66\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderBaseEvent.php.html#18\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::__construct<\/a>"],[0,2,"<a href=\"WorkOrderBaseEvent.php.html#23\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderBaseEvent.php.html#82\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderBaseEvent.php.html#92\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderBaseEvent.php.html#104\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderBaseEvent.php.html#114\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderDescriptionUpdated.php.html#14\">App\\Events\\WorkOrder\\WorkOrderDescriptionUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrderDetailsUpdate.php.html#22\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::__construct<\/a>"],[0,1,"<a href=\"WorkOrderDetailsUpdate.php.html#27\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderDetailsUpdate.php.html#37\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderDetailsUpdate.php.html#53\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderDetailsUpdate.php.html#63\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastWith<\/a>"],[0,2,"<a href=\"WorkOrderHealthScoreChange.php.html#29\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::__construct<\/a>"],[0,1,"<a href=\"WorkOrderHealthScoreChange.php.html#47\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderHealthScoreChange.php.html#57\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderHealthScoreChange.php.html#67\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderHealthScoreChange.php.html#77\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderListDeleted.php.html#24\">App\\Events\\WorkOrder\\WorkOrderListDeleted::__construct<\/a>"],[0,1,"<a href=\"WorkOrderListDeleted.php.html#31\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderListDeleted.php.html#41\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderListDeleted.php.html#56\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderListDeleted.php.html#66\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderPaused.php.html#17\">App\\Events\\WorkOrder\\WorkOrderPaused::__construct<\/a>"],[0,1,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#26\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#31\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#39\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::getPropertyDetails<\/a>"],[0,1,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#47\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#57\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#69\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderPropertyAddressUpdated.php.html#79\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderReOpen.php.html#17\">App\\Events\\WorkOrder\\WorkOrderReOpen::__construct<\/a>"],[0,1,"<a href=\"WorkOrderReScheduled.php.html#17\">App\\Events\\WorkOrder\\WorkOrderReScheduled::__construct<\/a>"],[0,1,"<a href=\"WorkOrderResidentUpdated.php.html#14\">App\\Events\\WorkOrder\\WorkOrderResidentUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrderResolve.php.html#17\">App\\Events\\WorkOrder\\WorkOrderResolve::__construct<\/a>"],[0,1,"<a href=\"WorkOrderSendToVendor.php.html#18\">App\\Events\\WorkOrder\\WorkOrderSendToVendor::__construct<\/a>"],[0,1,"<a href=\"WorkOrderSendToVendor.php.html#23\">App\\Events\\WorkOrder\\WorkOrderSendToVendor::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderSendToVendor.php.html#28\">App\\Events\\WorkOrder\\WorkOrderSendToVendor::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallAssigned.php.html#23\">App\\Events\\WorkOrder\\WorkOrderServiceCallAssigned::__construct<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallRescheduled.php.html#23\">App\\Events\\WorkOrder\\WorkOrderServiceCallRescheduled::__construct<\/a>"],[0,1,"<a href=\"WorkOrderStateChange.php.html#29\">App\\Events\\WorkOrder\\WorkOrderStateChange::__construct<\/a>"],[0,1,"<a href=\"WorkOrderStateChange.php.html#40\">App\\Events\\WorkOrder\\WorkOrderStateChange::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderStateChange.php.html#53\">App\\Events\\WorkOrder\\WorkOrderStateChange::broadcastAs<\/a>"],[0,3,"<a href=\"WorkOrderStateChange.php.html#63\">App\\Events\\WorkOrder\\WorkOrderStateChange::broadcastWith<\/a>"],[0,2,"<a href=\"WorkOrderStateChange.php.html#112\">App\\Events\\WorkOrder\\WorkOrderStateChange::getWorkOrder<\/a>"],[0,2,"<a href=\"WorkOrderStateChange.php.html#149\">App\\Events\\WorkOrder\\WorkOrderStateChange::getWorkOrderActivityLog<\/a>"],[0,1,"<a href=\"WorkOrderTagUpdated.php.html#25\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrderTagUpdated.php.html#36\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderTagUpdated.php.html#46\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderTagUpdated.php.html#56\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderTagUpdated.php.html#66\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderUpdate.php.html#22\">App\\Events\\WorkOrder\\WorkOrderUpdate::__construct<\/a>"],[0,1,"<a href=\"WorkOrderUpdate.php.html#30\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrderUpdate.php.html#40\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrderUpdate.php.html#55\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderUpdate.php.html#65\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastWith<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
