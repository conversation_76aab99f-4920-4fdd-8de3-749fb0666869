<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Services</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Services</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Ability/AbilityResolver.php.html#15">App\Services\Ability\AbilityResolver</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#16">App\Services\Ability\Strategies\IssueAbilityStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#16">App\Services\Ability\Strategies\WorkOrderAbilityStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#15">App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#37">App\Services\Appfolio\AppfolioForServiceRequestService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#36">App\Services\Appfolio\AppfolioService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#11">App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#43">App\Services\InvoiceRegister\InvoiceRegisterClient</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/CreateOrUpdateInvoice.php.html#12">App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateMaterials.php.html#10">App\Services\InvoiceRegister\Pipes\UpdateMaterials</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#28">App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateQuoteTasks.php.html#11">App\Services\InvoiceRegister\Pipes\UpdateQuoteTasks</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateServiceCalls.php.html#11">App\Services\InvoiceRegister\Pipes\UpdateServiceCalls</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/Appointments.php.html#15">App\Services\Scheduling\Domain\Collections\Appointments</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/Technicians.php.html#14">App\Services\Scheduling\Domain\Collections\Technicians</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/WorkingHours.php.html#19">App\Services\Scheduling\Domain\Collections\WorkingHours</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/AvailableProviders.php.html#9">App\Services\Scheduling\Domain\DTOs\AvailableProviders</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/TaskSchedulingOptions.php.html#10">App\Services\Scheduling\Domain\DTOs\TaskSchedulingOptions</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/TechnicianList.php.html#9">App\Services\Scheduling\Domain\DTOs\TechnicianList</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/TechnicianSchedule.php.html#9">App\Services\Scheduling\Domain\DTOs\TechnicianSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/AgendaSlot.php.html#7">App\Services\Scheduling\Domain\Entities\AgendaSlot</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Appointment.php.html#10">App\Services\Scheduling\Domain\Entities\Appointment</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#15">App\Services\Scheduling\Domain\Entities\DailyAgenda</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Location.php.html#9">App\Services\Scheduling\Domain\Entities\Location</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/RankedServiceWindow.php.html#5">App\Services\Scheduling\Domain\Entities\RankedServiceWindow</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#10">App\Services\Scheduling\Domain\Entities\ServiceWindow</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Technician.php.html#9">App\Services\Scheduling\Domain\Entities\Technician</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#17">App\Services\Scheduling\Domain\Entities\TechnicianCalendar</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrder.php.html#8">App\Services\Scheduling\Domain\Entities\WorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrderQuote.php.html#8">App\Services\Scheduling\Domain\Entities\WorkOrderQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkingDay.php.html#12">App\Services\Scheduling\Domain\Entities\WorkingDay</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#26">App\Services\Scheduling\SchedulingRepository</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#37">App\Services\Scheduling\SchedulingService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Strategies/EarliestStrategy.php.html#13">App\Services\Scheduling\Strategies\EarliestStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Strategies/EfficientStrategy.php.html#13">App\Services\Scheduling\Strategies\EfficientStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Strategies/EmergencyStrategy.php.html#12">App\Services\Scheduling\Strategies\EmergencyStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Domain/Exceptions/TripStateServiceException.php.html#7">App\Services\ServiceCall\Trip\Domain\Exceptions\TripStateServiceException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/EntityResolver.php.html#11">App\Services\ServiceCall\Trip\EntityResolver</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Lula/EnRouteHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Lula\EnRouteHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRouteHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRoutePausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EndedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/PausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/WorkingHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/EnRouteHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\EnRouteHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/EnRoutePausedHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\EnRoutePausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/EndedHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\EndedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/PausedHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\PausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/WorkingHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\WorkingHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/StateTransitionHandlerFactory.php.html#12">App\Services\ServiceCall\Trip\StateTransitionHandlerFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/TripStateService.php.html#10">App\Services\ServiceCall\Trip\TripStateService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/InternalNotesLeaf.php.html#10">App\Services\ServiceRequestActivity\Leaf\InternalNotesLeaf</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/StatusChangeLeaf.php.html#10">App\Services\ServiceRequestActivity\Leaf\StatusChangeLeaf</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#13">App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/PropertyRegister.php.html#13">App\Services\ServiceRequestRegister\Pipes\PropertyRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ResidentRegister.php.html#11">App\Services\ServiceRequestRegister\Pipes\ResidentRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestCategoryRegister.php.html#10">App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#16">App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestRegister.php.html#17">App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/ServiceRequestRegisterClient.php.html#25">App\Services\ServiceRequestRegister\ServiceRequestRegisterClient</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Configuration.php.html#5">App\Services\Vendor\Entities\Configuration</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Organization.php.html#8">App\Services\Vendor\Entities\Organization</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/WorkOrder.php.html#7">App\Services\Vendor\Entities\WorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Exceptions/ServiceException.php.html#7">App\Services\Vendor\Exceptions\ServiceException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Factory/VendorServiceFactory.php.html#12">App\Services\Vendor\Factory\VendorServiceFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#15">App\Services\Vendor\Services\Lula\Authentication</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#8">App\Services\Vendor\Services\Lula\Config</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/AuthenticationException.php.html#7">App\Services\Vendor\Services\Lula\Exception\AuthenticationException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#7">App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaClient.php.html#10">App\Services\Vendor\Services\Lula\LulaClient</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#42">App\Services\Vendor\Services\Lula\LulaService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Webhook/LulaWebhookConfig.php.html#7">App\Services\Vendor\Services\Lula\Webhook\LulaWebhookConfig</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Webhook/LulaWebhookSignatureValidator.php.html#10">App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#33">App\Services\Vendor\Services\ThirdParty\ThirdPartyService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorService.php.html#10">App\Services\Vendor\VendorService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#7">App\Services\VideoStream\VideoStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/WebhookClientProcessor.php.html#9">App\Services\Webhook\WebhookClientProcessor</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/WebhookRetryBackoffStrategy.php.html#7">App\Services\Webhook\WebhookRetryBackoffStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#19">App\Services\WorkOrderActivity\ActivityComposite</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/InternalNotesLeaf.php.html#9">App\Services\WorkOrderActivity\Leaf\InternalNotesLeaf</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/StatusChangeLeaf.php.html#10">App\Services\WorkOrderActivity\Leaf\StatusChangeLeaf</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#14">App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/PropertyRegister.php.html#14">App\Services\WorkOrderRegister\Pipes\PropertyRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/ResidentRegister.php.html#12">App\Services\WorkOrderRegister\Pipes\ResidentRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderMediaRegister.php.html#10">App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderRegister.php.html#13">App\Services\WorkOrderRegister\Pipes\WorkOrderRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderTaskRegister.php.html#11">App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/WorkOrderRegisterClient.php.html#31">App\Services\WorkOrderRegister\WorkOrderRegisterClient</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Enums/CarbonDayOfWeek.php.html#8">App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Traits/DistanceHelperTrait.php.html#7">App\Services\Scheduling\Domain\Traits\DistanceHelperTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Enum/Service.php.html#9">App\Services\Vendor\Enum\Service</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Enum/ServiceMode.php.html#5">App\Services\Vendor\Enum\ServiceMode</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Scope/WorkOrderScope.php.html#8">App\Services\Vendor\Services\Lula\Scope\WorkOrderScope</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#8">App\Services\Aws\LocationService</a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#40">App\Services\AssigneeFilterService</a></td><td class="text-right">30%</td></tr>
       <tr><td><a href="SearchService.php.html#30">App\Services\SearchService</a></td><td class="text-right">36%</td></tr>
       <tr><td><a href="ServiceCall/Trip/TripStateServiceProvider.php.html#7">App\Services\ServiceCall\Trip\TripStateServiceProvider</a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="FilterService.php.html#43">App\Services\FilterService</a></td><td class="text-right">59%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Appfolio/AppfolioService.php.html#36">App\Services\Appfolio\AppfolioService</a></td><td class="text-right">28730</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#37">App\Services\Appfolio\AppfolioForServiceRequestService</a></td><td class="text-right">22350</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#28">App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems</a></td><td class="text-right">5852</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#43">App\Services\InvoiceRegister\InvoiceRegisterClient</a></td><td class="text-right">5256</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#42">App\Services\Vendor\Services\Lula\LulaService</a></td><td class="text-right">1332</td></tr>
       <tr><td><a href="FilterService.php.html#43">App\Services\FilterService</a></td><td class="text-right">1309</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#37">App\Services\Scheduling\SchedulingService</a></td><td class="text-right">1260</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#15">App\Services\Scheduling\Domain\Entities\DailyAgenda</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#17">App\Services\Scheduling\Domain\Entities\TechnicianCalendar</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#15">App\Services\Vendor\Services\Lula\Authentication</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#40">App\Services\AssigneeFilterService</a></td><td class="text-right">480</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#7">App\Services\VideoStream\VideoStreamer</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#33">App\Services\Vendor\Services\ThirdParty\ThirdPartyService</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#15">App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#16">App\Services\Ability\Strategies\IssueAbilityStrategy</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#16">App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="WorkOrderRegister/WorkOrderRegisterClient.php.html#31">App\Services\WorkOrderRegister\WorkOrderRegisterClient</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#16">App\Services\Ability\Strategies\WorkOrderAbilityStrategy</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#10">App\Services\Scheduling\Domain\Entities\ServiceWindow</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#26">App\Services\Scheduling\SchedulingRepository</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ResidentRegister.php.html#11">App\Services\ServiceRequestRegister\Pipes\ResidentRegister</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#19">App\Services\WorkOrderActivity\ActivityComposite</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/ResidentRegister.php.html#12">App\Services\WorkOrderRegister\Pipes\ResidentRegister</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderRegister.php.html#13">App\Services\WorkOrderRegister\Pipes\WorkOrderRegister</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestRegister.php.html#17">App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#8">App\Services\Vendor\Services\Lula\Config</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#8">App\Services\Aws\LocationService</a></td><td class="text-right">45</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/CreateOrUpdateInvoice.php.html#12">App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/StatusChangeLeaf.php.html#10">App\Services\ServiceRequestActivity\Leaf\StatusChangeLeaf</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestRegister/ServiceRequestRegisterClient.php.html#25">App\Services\ServiceRequestRegister\ServiceRequestRegisterClient</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/StatusChangeLeaf.php.html#10">App\Services\WorkOrderActivity\Leaf\StatusChangeLeaf</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Scheduling/Domain/Traits/DistanceHelperTrait.php.html#7">App\Services\Scheduling\Domain\Traits\DistanceHelperTrait</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/WorkingHours.php.html#19">App\Services\Scheduling\Domain\Collections\WorkingHours</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Appointment.php.html#10">App\Services\Scheduling\Domain\Entities\Appointment</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceCall/Trip/EntityResolver.php.html#11">App\Services\ServiceCall\Trip\EntityResolver</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestCategoryRegister.php.html#10">App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Vendor/Entities/Organization.php.html#8">App\Services\Vendor\Entities\Organization</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderMediaRegister.php.html#10">App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderTaskRegister.php.html#11">App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SearchService.php.html#30">App\Services\SearchService</a></td><td class="text-right">19</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrder.php.html#8">App\Services\Scheduling\Domain\Entities\WorkOrder</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRouteHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EndedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/PausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/WorkingHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/StateTransitionHandlerFactory.php.html#12">App\Services\ServiceCall\Trip\StateTransitionHandlerFactory</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/Lula/Webhook/LulaWebhookSignatureValidator.php.html#10">App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRoutePausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/PropertyRegister.php.html#13">App\Services\ServiceRequestRegister\Pipes\PropertyRegister</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Webhook/WebhookClientProcessor.php.html#9">App\Services\Webhook\WebhookClientProcessor</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/PropertyRegister.php.html#14">App\Services\WorkOrderRegister\Pipes\PropertyRegister</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Ability/AbilityResolver.php.html#17"><abbr title="App\Services\Ability\AbilityResolver::resolve">resolve</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#40"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isDeleted">isDeleted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#51"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterDeleteAction">filterDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#66"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterCancelAction">filterCancelAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#82"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isMappedToActiveWorkOrder">isMappedToActiveWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#94"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isClosedServiceRequest">isClosedServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#43"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::abilitiesForWorkInProgressStatus">abilitiesForWorkInProgressStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#62"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::filterMobileAbilities">filterMobileAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#91"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::removeActions">removeActions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#17"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#38"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::isDeleted">isDeleted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#49"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterEditAction">filterEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#61"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::isMappedToClosedWorkOrder">isMappedToClosedWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#76"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterUnusedAbilitiesForMobile">filterUnusedAbilitiesForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#93"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#108"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::get">get</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#132"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::post">post</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#152"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::patch">patch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#171"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::postAttachment">postAttachment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#196"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#222"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::unitDetails">unitDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#249"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getTenants">getTenants</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#281"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#305"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::ingestNewWorkOrdersToServiceRequests">ingestNewWorkOrdersToServiceRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#403"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#505"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#568"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatServiceRequestPayload">formatServiceRequestPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#619"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#639"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#671"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#682"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#710"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#808"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#818"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::connect">connect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#835"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#857"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#945"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getAppfolioWorkOrders">getAppfolioWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#962"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#987"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::createNewServiceRequest">createNewServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1041"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1069"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1129"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1146"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1176"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1212"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1248"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addAppfolioIntegrationSkippedWorkOrders">addAppfolioIntegrationSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#92"><abbr title="App\Services\Appfolio\AppfolioService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#107"><abbr title="App\Services\Appfolio\AppfolioService::get">get</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#131"><abbr title="App\Services\Appfolio\AppfolioService::post">post</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#151"><abbr title="App\Services\Appfolio\AppfolioService::patch">patch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#170"><abbr title="App\Services\Appfolio\AppfolioService::postAttachment">postAttachment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#190"><abbr title="App\Services\Appfolio\AppfolioService::createNotes">createNotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#219"><abbr title="App\Services\Appfolio\AppfolioService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#245"><abbr title="App\Services\Appfolio\AppfolioService::unitDetails">unitDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#272"><abbr title="App\Services\Appfolio\AppfolioService::getTenants">getTenants</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#304"><abbr title="App\Services\Appfolio\AppfolioService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#327"><abbr title="App\Services\Appfolio\AppfolioService::ingestNewWorkOrders">ingestNewWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#417"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#574"><abbr title="App\Services\Appfolio\AppfolioService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#637"><abbr title="App\Services\Appfolio\AppfolioService::formatWorkOrderPayload">formatWorkOrderPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#687"><abbr title="App\Services\Appfolio\AppfolioService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#707"><abbr title="App\Services\Appfolio\AppfolioService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#739"><abbr title="App\Services\Appfolio\AppfolioService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#750"><abbr title="App\Services\Appfolio\AppfolioService::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#777"><abbr title="App\Services\Appfolio\AppfolioService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#787"><abbr title="App\Services\Appfolio\AppfolioService::connect">connect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#804"><abbr title="App\Services\Appfolio\AppfolioService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#826"><abbr title="App\Services\Appfolio\AppfolioService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#914"><abbr title="App\Services\Appfolio\AppfolioService::getWorkOrders">getWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#931"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1022"><abbr title="App\Services\Appfolio\AppfolioService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1045"><abbr title="App\Services\Appfolio\AppfolioService::syncExistingWorkOrders">syncExistingWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1088"><abbr title="App\Services\Appfolio\AppfolioService::validateAndUpdateJobs">validateAndUpdateJobs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1098"><abbr title="App\Services\Appfolio\AppfolioService::validatePropertyLocation">validatePropertyLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1111"><abbr title="App\Services\Appfolio\AppfolioService::requestNewWorkOrder">requestNewWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1191"><abbr title="App\Services\Appfolio\AppfolioService::findServiceCategory">findServiceCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1209"><abbr title="App\Services\Appfolio\AppfolioService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1269"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1286"><abbr title="App\Services\Appfolio\AppfolioService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1305"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1316"><abbr title="App\Services\Appfolio\AppfolioService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1352"><abbr title="App\Services\Appfolio\AppfolioService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1388"><abbr title="App\Services\Appfolio\AppfolioService::addAppfolioIntegrationSkippedWorkOrders">addAppfolioIntegrationSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#102"><abbr title="App\Services\AssigneeFilterService::applyNotAssignedToFilter">applyNotAssignedToFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#290"><abbr title="App\Services\AssigneeFilterService::applyQuoteAssigneeFilter">applyQuoteAssigneeFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#344"><abbr title="App\Services\AssigneeFilterService::applyServiceRequestAssigneeFilter">applyServiceRequestAssigneeFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#393"><abbr title="App\Services\AssigneeFilterService::getWorkOrderIdsWithAssignees">getWorkOrderIdsWithAssignees</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#406"><abbr title="App\Services\AssigneeFilterService::getQuoteIdsWithAssignees">getQuoteIdsWithAssignees</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#420"><abbr title="App\Services\AssigneeFilterService::getServiceRequestIdsWithAssignees">getServiceRequestIdsWithAssignees</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#22"><abbr title="App\Services\Aws\LocationService::searchPlace">searchPlace</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#45"><abbr title="App\Services\Aws\LocationService::searchNearPosition">searchNearPosition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#67"><abbr title="App\Services\Aws\LocationService::handleAwsException">handleAwsException</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterService.php.html#595"><abbr title="App\Services\FilterService::generateWorkOrderQuery">generateWorkOrderQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterService.php.html#638"><abbr title="App\Services\FilterService::generateVendorWorkOrderQuery">generateVendorWorkOrderQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterService.php.html#674"><abbr title="App\Services\FilterService::generateServiceRequestQuery">generateServiceRequestQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterService.php.html#706"><abbr title="App\Services\FilterService::generateUserQuery">generateUserQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterService.php.html#735"><abbr title="App\Services\FilterService::generateQuoteQuery">generateQuoteQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterService.php.html#958"><abbr title="App\Services\FilterService::applySubmittedByFilter">applySubmittedByFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterService.php.html#984"><abbr title="App\Services\FilterService::applyTagWorkOrderIdFilter">applyTagWorkOrderIdFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#13"><abbr title="App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#21"><abbr title="App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject::getRequest">getRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#26"><abbr title="App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#31"><abbr title="App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#36"><abbr title="App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject::getWorkOrderTask">getWorkOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#41"><abbr title="App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject::setInvoice">setInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/DTO/InvoiceRegisterDataObject.php.html#46"><abbr title="App\Services\InvoiceRegister\DTO\InvoiceRegisterDataObject::getInvoice">getInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#45"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#116"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::generateInvoiceDetailsFromWorkOrderServiceCalls">generateInvoiceDetailsFromWorkOrderServiceCalls</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#283"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::generateInvoiceDetailsFromVendorExternalInvoiceDetails">generateInvoiceDetailsFromVendorExternalInvoiceDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#425"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::getQuoteDetails">getQuoteDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#507"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::getHourlyDetails">getHourlyDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#627"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::getNoWorkDetails">getNoWorkDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#653"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::tripStatusLabel">tripStatusLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/CreateOrUpdateInvoice.php.html#14"><abbr title="App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateMaterials.php.html#12"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateMaterials::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#30"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#262"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::createOrUpdateSubsidiaries">createOrUpdateSubsidiaries</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#370"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::validateMedia">validateMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateQuoteTasks.php.html#13"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateQuoteTasks::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateServiceCalls.php.html#13"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateServiceCalls::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/Appointments.php.html#20"><abbr title="App\Services\Scheduling\Domain\Collections\Appointments::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/Appointments.php.html#25"><abbr title="App\Services\Scheduling\Domain\Collections\Appointments::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/Technicians.php.html#19"><abbr title="App\Services\Scheduling\Domain\Collections\Technicians::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/Technicians.php.html#27"><abbr title="App\Services\Scheduling\Domain\Collections\Technicians::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/WorkingHours.php.html#24"><abbr title="App\Services\Scheduling\Domain\Collections\WorkingHours::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/WorkingHours.php.html#29"><abbr title="App\Services\Scheduling\Domain\Collections\WorkingHours::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/AvailableProviders.php.html#15"><abbr title="App\Services\Scheduling\Domain\DTOs\AvailableProviders::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/TaskSchedulingOptions.php.html#16"><abbr title="App\Services\Scheduling\Domain\DTOs\TaskSchedulingOptions::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/TechnicianList.php.html#16"><abbr title="App\Services\Scheduling\Domain\DTOs\TechnicianList::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/DTOs/TechnicianSchedule.php.html#15"><abbr title="App\Services\Scheduling\Domain\DTOs\TechnicianSchedule::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/AgendaSlot.php.html#9"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/AgendaSlot.php.html#11"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::getStartTime">getStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/AgendaSlot.php.html#16"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::getEndTime">getEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/AgendaSlot.php.html#21"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::getAppointment">getAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Appointment.php.html#12"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Appointment.php.html#20"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Appointment.php.html#39"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::is">is</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#27"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#45"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::setAppointment">setAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#119"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getOpenSlots">getOpenSlots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#131"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getSlotsByAppointment">getSlotsByAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#149"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getOccupiedSlots">getOccupiedSlots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#154"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getAgendaDate">getAgendaDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#163"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::findAndAppendTimeIntervals">findAndAppendTimeIntervals</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#180"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getAgendaSlotByTime">getAgendaSlotByTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#198"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getRequiredTimeIntervals">getRequiredTimeIntervals</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Location.php.html#11"><abbr title="App\Services\Scheduling\Domain\Entities\Location::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Location.php.html#13"><abbr title="App\Services\Scheduling\Domain\Entities\Location::fromProperty">fromProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Location.php.html#18"><abbr title="App\Services\Scheduling\Domain\Entities\Location::fromTechnician">fromTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/RankedServiceWindow.php.html#7"><abbr title="App\Services\Scheduling\Domain\Entities\RankedServiceWindow::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#18"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#28"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::fromReference">fromReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#45"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getResidentStartTime">getResidentStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#50"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getResidentEndTime">getResidentEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#55"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getProviderStartTime">getProviderStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#60"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getProviderEndTime">getProviderEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#65"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::setEndTime">setEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#70"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::setDestination">setDestination</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#83"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::encodedReference">encodedReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Technician.php.html#11"><abbr title="App\Services\Scheduling\Domain\Entities\Technician::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Technician.php.html#22"><abbr title="App\Services\Scheduling\Domain\Entities\Technician::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#25"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#55"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::isAvailableServiceWindow">isAvailableServiceWindow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#79"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getOpenServiceWindows">getOpenServiceWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#163"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::applyAppointments">applyAppointments</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#194"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getOccupiedDays">getOccupiedDays</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#204"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getFreeSlots">getFreeSlots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#211"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getNearestAppointment">getNearestAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrder.php.html#10"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrder.php.html#27"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrder::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrderQuote.php.html#10"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrderQuote::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrderQuote.php.html#12"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrderQuote::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkingDay.php.html#14"><abbr title="App\Services\Scheduling\Domain\Entities\WorkingDay::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkingDay.php.html#19"><abbr title="App\Services\Scheduling\Domain\Entities\WorkingDay::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkingDay.php.html#28"><abbr title="App\Services\Scheduling\Domain\Entities\WorkingDay::is">is</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#31"><abbr title="App\Services\Scheduling\SchedulingRepository::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#49"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechniciansForTask">getTechniciansForTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#108"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechnicianByUUID">getTechnicianByUUID</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#169"><abbr title="App\Services\Scheduling\SchedulingRepository::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#41"><abbr title="App\Services\Scheduling\SchedulingService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#43"><abbr title="App\Services\Scheduling\SchedulingService::getTaskSchedulingOptions">getTaskSchedulingOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#104"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#173"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#225"><abbr title="App\Services\Scheduling\SchedulingService::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#275"><abbr title="App\Services\Scheduling\SchedulingService::findStartAndEndTimes">findStartAndEndTimes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#305"><abbr title="App\Services\Scheduling\SchedulingService::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#313"><abbr title="App\Services\Scheduling\SchedulingService::getScheduler">getScheduler</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#322"><abbr title="App\Services\Scheduling\SchedulingService::getEstimatedDuration">getEstimatedDuration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#327"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentDuration">getCurrentDuration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#332"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentMode">getCurrentMode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#337"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentMethod">getCurrentMethod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#342"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentWorkPerform">getCurrentWorkPerform</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#350"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentLinkedQuote">getCurrentLinkedQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#365"><abbr title="App\Services\Scheduling\SchedulingService::getQualifiedTechnicians">getQualifiedTechnicians</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#391"><abbr title="App\Services\Scheduling\SchedulingService::filterWindowsByResidentAvailability">filterWindowsByResidentAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Strategies/EarliestStrategy.php.html#19"><abbr title="App\Services\Scheduling\Strategies\EarliestStrategy::getPreferredWindows">getPreferredWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Strategies/EfficientStrategy.php.html#19"><abbr title="App\Services\Scheduling\Strategies\EfficientStrategy::getPreferredWindows">getPreferredWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Strategies/EmergencyStrategy.php.html#18"><abbr title="App\Services\Scheduling\Strategies\EmergencyStrategy::getPreferredWindows">getPreferredWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Domain/Exceptions/TripStateServiceException.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Domain\Exceptions\TripStateServiceException::invalidEntityResolver">invalidEntityResolver</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Domain/Exceptions/TripStateServiceException.php.html#14"><abbr title="App\Services\ServiceCall\Trip\Domain\Exceptions\TripStateServiceException::invalidStateTransitionArgumentException">invalidStateTransitionArgumentException</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/EntityResolver.php.html#20"><abbr title="App\Services\ServiceCall\Trip\EntityResolver::resolve">resolve</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Lula/EnRouteHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Lula\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRouteHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRoutePausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EndedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/PausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/WorkingHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/EnRouteHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/EnRoutePausedHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\EnRoutePausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/EndedHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\EndedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/PausedHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\PausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Vendor/WorkingHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\WorkingHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/StateTransitionHandlerFactory.php.html#16"><abbr title="App\Services\ServiceCall\Trip\StateTransitionHandlerFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/StateTransitionHandlerFactory.php.html#27"><abbr title="App\Services\ServiceCall\Trip\StateTransitionHandlerFactory::make">make</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/TripStateService.php.html#15"><abbr title="App\Services\ServiceCall\Trip\TripStateService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCall/Trip/TripStateService.php.html#24"><abbr title="App\Services\ServiceCall\Trip\TripStateService::updateTripState">updateTripState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/InternalNotesLeaf.php.html#12"><abbr title="App\Services\ServiceRequestActivity\Leaf\InternalNotesLeaf::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/InternalNotesLeaf.php.html#17"><abbr title="App\Services\ServiceRequestActivity\Leaf\InternalNotesLeaf::getActivities">getActivities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/StatusChangeLeaf.php.html#12"><abbr title="App\Services\ServiceRequestActivity\Leaf\StatusChangeLeaf::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/StatusChangeLeaf.php.html#17"><abbr title="App\Services\ServiceRequestActivity\Leaf\StatusChangeLeaf::getActivities">getActivities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#15"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#25"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::setRequest">setRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#30"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::getRequest">getRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#35"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#40"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::getOrganization">getOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#45"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::setUser">setUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#50"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#55"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::setTimezone">setTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#60"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::getTimezone">getTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#65"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::setProperty">setProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#70"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::getProperty">getProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#75"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::setResident">setResident</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#80"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::getResident">getResident</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#85"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::setServiceRequest">setServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Dto/ServiceRequestRegisterTransport.php.html#90"><abbr title="App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/PropertyRegister.php.html#15"><abbr title="App\Services\ServiceRequestRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ResidentRegister.php.html#13"><abbr title="App\Services\ServiceRequestRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestCategoryRegister.php.html#12"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#18"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#41"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::isAppfolioSource">isAppfolioSource</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#48"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::dispatchPhotoJobs">dispatchPhotoJobs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#67"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::updateAdditionalInfo">updateAdditionalInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestRegister.php.html#19"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister/ServiceRequestRegisterClient.php.html#27"><abbr title="App\Services\ServiceRequestRegister\ServiceRequestRegisterClient::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Configuration.php.html#7"><abbr title="App\Services\Vendor\Entities\Configuration::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Configuration.php.html#15"><abbr title="App\Services\Vendor\Entities\Configuration::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Configuration.php.html#23"><abbr title="App\Services\Vendor\Entities\Configuration::getClientId">getClientId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Configuration.php.html#28"><abbr title="App\Services\Vendor\Entities\Configuration::getSecretKey">getSecretKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Organization.php.html#10"><abbr title="App\Services\Vendor\Entities\Organization::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/Organization.php.html#12"><abbr title="App\Services\Vendor\Entities\Organization::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/WorkOrder.php.html#9"><abbr title="App\Services\Vendor\Entities\WorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Entities/WorkOrder.php.html#11"><abbr title="App\Services\Vendor\Entities\WorkOrder::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Exceptions/ServiceException.php.html#9"><abbr title="App\Services\Vendor\Exceptions\ServiceException::invalidService">invalidService</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Exceptions/ServiceException.php.html#14"><abbr title="App\Services\Vendor\Exceptions\ServiceException::tokenExpired">tokenExpired</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Exceptions/ServiceException.php.html#19"><abbr title="App\Services\Vendor\Exceptions\ServiceException::invalidToken">invalidToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Exceptions/ServiceException.php.html#24"><abbr title="App\Services\Vendor\Exceptions\ServiceException::invalidCacheKey">invalidCacheKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Exceptions/ServiceException.php.html#29"><abbr title="App\Services\Vendor\Exceptions\ServiceException::invalidKey">invalidKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Exceptions/ServiceException.php.html#34"><abbr title="App\Services\Vendor\Exceptions\ServiceException::invalidVendor">invalidVendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Factory/VendorServiceFactory.php.html#17"><abbr title="App\Services\Vendor\Factory\VendorServiceFactory::provider">provider</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#38"><abbr title="App\Services\Vendor\Services\Lula\Authentication::authenticate">authenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#55"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getToken">getToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#65"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setClientId">setClientId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#72"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setClientKey">setClientKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#83"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setScopes">setScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#90"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setCacheKey">setCacheKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#97"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#116"><abbr title="App\Services\Vendor\Services\Lula\Authentication::invalidateToken">invalidateToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#131"><abbr title="App\Services\Vendor\Services\Lula\Authentication::attempt">attempt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#162"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getAuthorizationToken">getAuthorizationToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#167"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getCacheKey">getCacheKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#172"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setToken">setToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Config::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Config::getInstance">getInstance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#36"><abbr title="App\Services\Vendor\Services\Lula\Config::baseUrl">baseUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#49"><abbr title="App\Services\Vendor\Services\Lula\Config::version">version</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#54"><abbr title="App\Services\Vendor\Services\Lula\Config::getEnvironmentBaseUrl">getEnvironmentBaseUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/AuthenticationException.php.html#9"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::invalidClient">invalidClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/AuthenticationException.php.html#14"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::invalidClientKey">invalidClientKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/AuthenticationException.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::invalidRequest">invalidRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/AuthenticationException.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::accessTokenMissing">accessTokenMissing</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#9"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::emptyWorkOrderTask">emptyWorkOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#14"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::serviceCallRegisterFailed">serviceCallRegisterFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::failedResponse">failedResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::lulaAppointmentRegisterFailed">lulaAppointmentRegisterFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#29"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::lulaWorkOrder">lulaWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#34"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::unexpectedResponseData">unexpectedResponseData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#39"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::emptyVendor">emptyVendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Exception/WorkOrderSendException.php.html#44"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::vendorAppointmentRegisterFailed">vendorAppointmentRegisterFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaClient.php.html#12"><abbr title="App\Services\Vendor\Services\Lula\LulaClient::getInstance">getInstance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaClient.php.html#22"><abbr title="App\Services\Vendor\Services\Lula\LulaClient::send">send</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaClient.php.html#36"><abbr title="App\Services\Vendor\Services\Lula\LulaClient::__callStatic">__callStatic</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#50"><abbr title="App\Services\Vendor\Services\Lula\LulaService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#55"><abbr title="App\Services\Vendor\Services\Lula\LulaService::authenticate">authenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#62"><abbr title="App\Services\Vendor\Services\Lula\LulaService::isAuthenticate">isAuthenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#67"><abbr title="App\Services\Vendor\Services\Lula\LulaService::token">token</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#72"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#92"><abbr title="App\Services\Vendor\Services\Lula\LulaService::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#104"><abbr title="App\Services\Vendor\Services\Lula\LulaService::setMode">setMode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#118"><abbr title="App\Services\Vendor\Services\Lula\LulaService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#319"><abbr title="App\Services\Vendor\Services\Lula\LulaService::workOrderList">workOrderList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#329"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#376"><abbr title="App\Services\Vendor\Services\Lula\LulaService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Webhook/LulaWebhookConfig.php.html#16"><abbr title="App\Services\Vendor\Services\Lula\Webhook\LulaWebhookConfig::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Webhook/LulaWebhookSignatureValidator.php.html#12"><abbr title="App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#35"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::authenticate">authenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#42"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::token">token</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#52"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#203"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#209"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#216"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::setMode">setMode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#224"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#232"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorService.php.html#17"><abbr title="App\Services\Vendor\VendorService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorService.php.html#23"><abbr title="App\Services\Vendor\VendorService::make">make</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorService.php.html#28"><abbr title="App\Services\Vendor\VendorService::getInstance">getInstance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#16"><abbr title="App\Services\VideoStream\VideoStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#24"><abbr title="App\Services\VideoStream\VideoStreamer::start">start</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#35"><abbr title="App\Services\VideoStream\VideoStreamer::open">open</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#47"><abbr title="App\Services\VideoStream\VideoStreamer::setHeader">setHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#109"><abbr title="App\Services\VideoStream\VideoStreamer::end">end</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#118"><abbr title="App\Services\VideoStream\VideoStreamer::stream">stream</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/WebhookClientProcessor.php.html#11"><abbr title="App\Services\Webhook\WebhookClientProcessor::processWebhook">processWebhook</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/WebhookRetryBackoffStrategy.php.html#9"><abbr title="App\Services\Webhook\WebhookRetryBackoffStrategy::waitInSecondsAfterAttempt">waitInSecondsAfterAttempt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#30"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::addActivities">addActivities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#39"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::getActivities">getActivities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#58"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::paginate">paginate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#69"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::getActivityComponent">getActivityComponent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#80"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::addActivityComponent">addActivityComponent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/InternalNotesLeaf.php.html#11"><abbr title="App\Services\WorkOrderActivity\Leaf\InternalNotesLeaf::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/InternalNotesLeaf.php.html#16"><abbr title="App\Services\WorkOrderActivity\Leaf\InternalNotesLeaf::getActivities">getActivities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/StatusChangeLeaf.php.html#12"><abbr title="App\Services\WorkOrderActivity\Leaf\StatusChangeLeaf::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/StatusChangeLeaf.php.html#17"><abbr title="App\Services\WorkOrderActivity\Leaf\StatusChangeLeaf::getActivities">getActivities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#16"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#27"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setRequest">setRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#32"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getRequest">getRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#37"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#42"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getOrganization">getOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#47"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setUser">setUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#52"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#57"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setTimezone">setTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#62"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getTimezone">getTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#67"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setProperty">setProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#72"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getProperty">getProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#77"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setResident">setResident</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#82"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getResident">getResident</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#87"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setWorkOrder">setWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#92"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#97"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setServiceRequest">setServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/DTO/WorkOrderRegisterTransport.php.html#102"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/PropertyRegister.php.html#20"><abbr title="App\Services\WorkOrderRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/ResidentRegister.php.html#14"><abbr title="App\Services\WorkOrderRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderMediaRegister.php.html#12"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderRegister.php.html#15"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderTaskRegister.php.html#13"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/WorkOrderRegisterClient.php.html#33"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegister/WorkOrderRegisterClient.php.html#137"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::assignIssueToWorkOrder">assignIssueToWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Enums/CarbonDayOfWeek.php.html#21"><abbr title="App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek::fromString">fromString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Enums/CarbonDayOfWeek.php.html#35"><abbr title="App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek::toString">toString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduling/Domain/Traits/DistanceHelperTrait.php.html#9"><abbr title="App\Services\Scheduling\Domain\Traits\DistanceHelperTrait::calculateStraightLineDistance">calculateStraightLineDistance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Enum/Service.php.html#17"><abbr title="App\Services\Vendor\Enum\Service::getServiceProviderFrom">getServiceProviderFrom</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Enum/ServiceMode.php.html#10"><abbr title="App\Services\Vendor\Enum\ServiceMode::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Scope/WorkOrderScope.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Scope\WorkOrderScope::all">all</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Scope/WorkOrderScope.php.html#28"><abbr title="App\Services\Vendor\Services\Lula\Scope\WorkOrderScope::generateScope">generateScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/Lula/Scope/WorkOrderScope.php.html#34"><abbr title="App\Services\Vendor\Services\Lula\Scope\WorkOrderScope::getPrefix">getPrefix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchService.php.html#101"><abbr title="App\Services\SearchService::registerDefaultSearchHandlers">registerDefaultSearchHandlers</abbr></a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="FilterService.php.html#369"><abbr title="App\Services\FilterService::applyEntityIdFilter">applyEntityIdFilter</abbr></a></td><td class="text-right">37%</td></tr>
       <tr><td><a href="FilterService.php.html#504"><abbr title="App\Services\FilterService::findWorkOrderFilterColumn">findWorkOrderFilterColumn</abbr></a></td><td class="text-right">38%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#195"><abbr title="App\Services\AssigneeFilterService::applyUnassignedFilter">applyUnassignedFilter</abbr></a></td><td class="text-right">43%</td></tr>
       <tr><td><a href="FilterService.php.html#524"><abbr title="App\Services\FilterService::findVendorWorkOrderFilterColumn">findVendorWorkOrderFilterColumn</abbr></a></td><td class="text-right">45%</td></tr>
       <tr><td><a href="FilterService.php.html#220"><abbr title="App\Services\FilterService::generateQuery">generateQuery</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="FilterService.php.html#568"><abbr title="App\Services\FilterService::findQuoteFilterColumn">findQuoteFilterColumn</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="ServiceCall/Trip/TripStateServiceProvider.php.html#9"><abbr title="App\Services\ServiceCall\Trip\TripStateServiceProvider::register">register</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="FilterService.php.html#257"><abbr title="App\Services\FilterService::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">62%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#175"><abbr title="App\Services\AssigneeFilterService::applyAssignedToFilter">applyAssignedToFilter</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="FilterService.php.html#542"><abbr title="App\Services\FilterService::findServiceRequestFilterColumn">findServiceRequestFilterColumn</abbr></a></td><td class="text-right">71%</td></tr>
       <tr><td><a href="FilterService.php.html#939"><abbr title="App\Services\FilterService::getModelForField">getModelForField</abbr></a></td><td class="text-right">71%</td></tr>
       <tr><td><a href="FilterService.php.html#393"><abbr title="App\Services\FilterService::registerDefaultFilterHandlers">registerDefaultFilterHandlers</abbr></a></td><td class="text-right">73%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#56"><abbr title="App\Services\AssigneeFilterService::applyAssigneeFilter">applyAssigneeFilter</abbr></a></td><td class="text-right">75%</td></tr>
       <tr><td><a href="FilterService.php.html#272"><abbr title="App\Services\FilterService::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">76%</td></tr>
       <tr><td><a href="FilterService.php.html#173"><abbr title="App\Services\FilterService::filterQuery">filterQuery</abbr></a></td><td class="text-right">80%</td></tr>
       <tr><td><a href="SearchService.php.html#79"><abbr title="App\Services\SearchService::applySearch">applySearch</abbr></a></td><td class="text-right">83%</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#233"><abbr title="App\Services\AssigneeFilterService::applyWorkOrderAssigneeFilter">applyWorkOrderAssigneeFilter</abbr></a></td><td class="text-right">87%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#30"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::handle">handle</abbr></a></td><td class="text-right">1640</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#262"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::createOrUpdateSubsidiaries">createOrUpdateSubsidiaries</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#116"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::generateInvoiceDetailsFromWorkOrderServiceCalls">generateInvoiceDetailsFromWorkOrderServiceCalls</abbr></a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#417"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#857"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1069"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#826"><abbr title="App\Services\Appfolio\AppfolioService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1209"><abbr title="App\Services\Appfolio\AppfolioService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#118"><abbr title="App\Services\Vendor\Services\Lula\LulaService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#710"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#931"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#507"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::getHourlyDetails">getHourlyDetails</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Vendor/ThirdParty/ThirdPartyService.php.html#52"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#47"><abbr title="App\Services\VideoStream\VideoStreamer::setHeader">setHeader</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#403"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="FilterService.php.html#595"><abbr title="App\Services\FilterService::generateWorkOrderQuery">generateWorkOrderQuery</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#45"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::register">register</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ResidentRegister.php.html#13"><abbr title="App\Services\ServiceRequestRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/ResidentRegister.php.html#14"><abbr title="App\Services\WorkOrderRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderRegister.php.html#15"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderRegister::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="FilterService.php.html#638"><abbr title="App\Services\FilterService::generateVendorWorkOrderQuery">generateVendorWorkOrderQuery</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#79"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getOpenServiceWindows">getOpenServiceWindows</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestRegister.php.html#19"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="FilterService.php.html#735"><abbr title="App\Services\FilterService::generateQuoteQuery">generateQuoteQuery</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#45"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::setAppointment">setAppointment</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrderRegister/WorkOrderRegisterClient.php.html#33"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::register">register</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#637"><abbr title="App\Services\Appfolio\AppfolioService::formatWorkOrderPayload">formatWorkOrderPayload</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#425"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::getQuoteDetails">getQuoteDetails</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/UpdateOrCreateInvoiceLineItems.php.html#370"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::validateMedia">validateMedia</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#283"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::generateInvoiceDetailsFromVendorExternalInvoiceDetails">generateInvoiceDetailsFromVendorExternalInvoiceDetails</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#391"><abbr title="App\Services\Scheduling\SchedulingService::filterWindowsByResidentAvailability">filterWindowsByResidentAvailability</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#76"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterUnusedAbilitiesForMobile">filterUnusedAbilitiesForMobile</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1176"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1212"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1316"><abbr title="App\Services\Appfolio\AppfolioService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1352"><abbr title="App\Services\Appfolio\AppfolioService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#102"><abbr title="App\Services\AssigneeFilterService::applyNotAssignedToFilter">applyNotAssignedToFilter</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="FilterService.php.html#674"><abbr title="App\Services\FilterService::generateServiceRequestQuery">generateServiceRequestQuery</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InvoiceRegister/Pipes/CreateOrUpdateInvoice.php.html#14"><abbr title="App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#198"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getRequiredTimeIntervals">getRequiredTimeIntervals</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#169"><abbr title="App\Services\Scheduling\SchedulingRepository::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#329"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#305"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::ingestNewWorkOrdersToServiceRequests">ingestNewWorkOrdersToServiceRequests</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#505"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#568"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatServiceRequestPayload">formatServiceRequestPayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#835"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1129"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#327"><abbr title="App\Services\Appfolio\AppfolioService::ingestNewWorkOrders">ingestNewWorkOrders</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#574"><abbr title="App\Services\Appfolio\AppfolioService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#804"><abbr title="App\Services\Appfolio\AppfolioService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1269"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#290"><abbr title="App\Services\AssigneeFilterService::applyQuoteAssigneeFilter">applyQuoteAssigneeFilter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#344"><abbr title="App\Services\AssigneeFilterService::applyServiceRequestAssigneeFilter">applyServiceRequestAssigneeFilter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#653"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::tripStatusLabel">tripStatusLabel</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#275"><abbr title="App\Services\Scheduling\SchedulingService::findStartAndEndTimes">findStartAndEndTimes</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#18"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestRegister/ServiceRequestRegisterClient.php.html#27"><abbr title="App\Services\ServiceRequestRegister\ServiceRequestRegisterClient::register">register</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#376"><abbr title="App\Services\Vendor\Services\Lula\LulaService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Scheduling/Domain/Traits/DistanceHelperTrait.php.html#9"><abbr title="App\Services\Scheduling\Domain\Traits\DistanceHelperTrait::calculateStraightLineDistance">calculateStraightLineDistance</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#66"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterCancelAction">filterCancelAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#249"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getTenants">getTenants</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#639"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#987"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::createNewServiceRequest">createNewServiceRequest</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#272"><abbr title="App\Services\Appfolio\AppfolioService::getTenants">getTenants</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#707"><abbr title="App\Services\Appfolio\AppfolioService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1111"><abbr title="App\Services\Appfolio\AppfolioService::requestNewWorkOrder">requestNewWorkOrder</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FilterService.php.html#706"><abbr title="App\Services\FilterService::generateUserQuery">generateUserQuery</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#131"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getSlotsByAppointment">getSlotsByAppointment</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#163"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::applyAppointments">applyAppointments</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#104"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceCall/Trip/EntityResolver.php.html#20"><abbr title="App\Services\ServiceCall\Trip\EntityResolver::resolve">resolve</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestActivity/Leaf/StatusChangeLeaf.php.html#17"><abbr title="App\Services\ServiceRequestActivity\Leaf\StatusChangeLeaf::getActivities">getActivities</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestCategoryRegister.php.html#12"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#97"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setOrganization">setOrganization</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#118"><abbr title="App\Services\VideoStream\VideoStreamer::stream">stream</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#58"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::paginate">paginate</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderActivity/Leaf/StatusChangeLeaf.php.html#17"><abbr title="App\Services\WorkOrderActivity\Leaf\StatusChangeLeaf::getActivities">getActivities</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderMediaRegister.php.html#12"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/WorkOrderTaskRegister.php.html#13"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#51"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterDeleteAction">filterDeleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#62"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::filterMobileAbilities">filterMobileAbilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderAbilityStrategy.php.html#91"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::removeActions">removeActions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#17"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#49"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterEditAction">filterEditAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#108"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::get">get</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#682"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getVendors">getVendors</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#808"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#945"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getAppfolioWorkOrders">getAppfolioWorkOrders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#962"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1146"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#107"><abbr title="App\Services\Appfolio\AppfolioService::get">get</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#750"><abbr title="App\Services\Appfolio\AppfolioService::getVendors">getVendors</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#777"><abbr title="App\Services\Appfolio\AppfolioService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#914"><abbr title="App\Services\Appfolio\AppfolioService::getWorkOrders">getWorkOrders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1022"><abbr title="App\Services\Appfolio\AppfolioService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1045"><abbr title="App\Services\Appfolio\AppfolioService::syncExistingWorkOrders">syncExistingWorkOrders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1191"><abbr title="App\Services\Appfolio\AppfolioService::findServiceCategory">findServiceCategory</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1286"><abbr title="App\Services\Appfolio\AppfolioService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1305"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FilterService.php.html#958"><abbr title="App\Services\FilterService::applySubmittedByFilter">applySubmittedByFilter</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InvoiceRegister/InvoiceRegisterClient.php.html#627"><abbr title="App\Services\InvoiceRegister\InvoiceRegisterClient::getNoWorkDetails">getNoWorkDetails</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Scheduling/Domain/Collections/WorkingHours.php.html#29"><abbr title="App\Services\Scheduling\Domain\Collections\WorkingHours::from">from</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#55"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::isAvailableServiceWindow">isAvailableServiceWindow</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#43"><abbr title="App\Services\Scheduling\SchedulingService::getTaskSchedulingOptions">getTaskSchedulingOptions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#225"><abbr title="App\Services\Scheduling\SchedulingService::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRouteHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EndedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/PausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/WorkingHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#48"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::dispatchPhotoJobs">dispatchPhotoJobs</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#67"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::updateAdditionalInfo">updateAdditionalInfo</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/Entities/Organization.php.html#12"><abbr title="App\Services\Vendor\Entities\Organization::from">from</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#38"><abbr title="App\Services\Vendor\Services\Lula\Authentication::authenticate">authenticate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#131"><abbr title="App\Services\Vendor\Services\Lula\Authentication::attempt">attempt</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Config::getInstance">getInstance</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/Lula/LulaService.php.html#72"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getClient">getClient</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/Lula/Webhook/LulaWebhookSignatureValidator.php.html#12"><abbr title="App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator::isValid">isValid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#39"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::getActivities">getActivities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderRegister/WorkOrderRegisterClient.php.html#137"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::assignIssueToWorkOrder">assignIssueToWorkOrder</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#56"><abbr title="App\Services\AssigneeFilterService::applyAssigneeFilter">applyAssigneeFilter</abbr></a></td><td class="text-right">7</td></tr>
       <tr><td><a href="Ability/Strategies/IssueAbilityStrategy.php.html#82"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isMappedToActiveWorkOrder">isMappedToActiveWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Ability/Strategies/WorkOrderIssueAbilityStrategy.php.html#61"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::isMappedToClosedWorkOrder">isMappedToClosedWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#171"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::postAttachment">postAttachment</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#196"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#222"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::unitDetails">unitDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#281"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#619"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioForServiceRequestService.php.html#1041"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#170"><abbr title="App\Services\Appfolio\AppfolioService::postAttachment">postAttachment</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#190"><abbr title="App\Services\Appfolio\AppfolioService::createNotes">createNotes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#219"><abbr title="App\Services\Appfolio\AppfolioService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#245"><abbr title="App\Services\Appfolio\AppfolioService::unitDetails">unitDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#304"><abbr title="App\Services\Appfolio\AppfolioService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#687"><abbr title="App\Services\Appfolio\AppfolioService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/AppfolioService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#22"><abbr title="App\Services\Aws\LocationService::searchPlace">searchPlace</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#45"><abbr title="App\Services\Aws\LocationService::searchNearPosition">searchNearPosition</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Aws/LocationService.php.html#67"><abbr title="App\Services\Aws\LocationService::handleAwsException">handleAwsException</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FilterService.php.html#984"><abbr title="App\Services\FilterService::applyTagWorkOrderIdFilter">applyTagWorkOrderIdFilter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/Appointment.php.html#20"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::from">from</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/DailyAgenda.php.html#180"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getAgendaSlotByTime">getAgendaSlotByTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#28"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::fromReference">fromReference</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/ServiceWindow.php.html#70"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::setDestination">setDestination</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/TechnicianCalendar.php.html#25"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/Domain/Entities/WorkOrder.php.html#27"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrder::from">from</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#49"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechniciansForTask">getTechniciansForTask</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/SchedulingRepository.php.html#108"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechnicianByUUID">getTechnicianByUUID</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#173"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Scheduling/SchedulingService.php.html#350"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentLinkedQuote">getCurrentLinkedQuote</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceCall/Trip/Handlers/Technician/EnRoutePausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler::apply">apply</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceCall/Trip/StateTransitionHandlerFactory.php.html#27"><abbr title="App\Services\ServiceCall\Trip\StateTransitionHandlerFactory::make">make</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/PropertyRegister.php.html#15"><abbr title="App\Services\ServiceRequestRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestRegister/Pipes/ServiceRequestPhotosRegister.php.html#41"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::isAppfolioSource">isAppfolioSource</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#55"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getToken">getToken</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#116"><abbr title="App\Services\Vendor\Services\Lula\Authentication::invalidateToken">invalidateToken</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendor/Lula/Authentication.php.html#172"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setToken">setToken</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendor/Lula/Config.php.html#36"><abbr title="App\Services\Vendor\Services\Lula\Config::baseUrl">baseUrl</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="VideoStream/VideoStreamer.php.html#35"><abbr title="App\Services\VideoStream\VideoStreamer::open">open</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Webhook/WebhookClientProcessor.php.html#11"><abbr title="App\Services\Webhook\WebhookClientProcessor::processWebhook">processWebhook</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderActivity/ActivityComposite.php.html#30"><abbr title="App\Services\WorkOrderActivity\ActivityComposite::addActivities">addActivities</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderRegister/Pipes/PropertyRegister.php.html#20"><abbr title="App\Services\WorkOrderRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FilterService.php.html#173"><abbr title="App\Services\FilterService::filterQuery">filterQuery</abbr></a></td><td class="text-right">5</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#233"><abbr title="App\Services\AssigneeFilterService::applyWorkOrderAssigneeFilter">applyWorkOrderAssigneeFilter</abbr></a></td><td class="text-right">5</td></tr>
       <tr><td><a href="FilterService.php.html#220"><abbr title="App\Services\FilterService::generateQuery">generateQuery</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="FilterService.php.html#272"><abbr title="App\Services\FilterService::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="SearchService.php.html#79"><abbr title="App\Services\SearchService::applySearch">applySearch</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="AssigneeFilterService.php.html#195"><abbr title="App\Services\AssigneeFilterService::applyUnassignedFilter">applyUnassignedFilter</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:24:01 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([93,1,0,0,2,0,2,0,0,0,0,5], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([368,0,0,1,2,2,3,2,5,3,3,42], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Ability\/AbilityResolver.php.html#15\">App\\Services\\Ability\\AbilityResolver<\/a>"],[0,14,"<a href=\"Ability\/Strategies\/IssueAbilityStrategy.php.html#16\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy<\/a>"],[0,11,"<a href=\"Ability\/Strategies\/WorkOrderAbilityStrategy.php.html#16\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy<\/a>"],[0,15,"<a href=\"Ability\/Strategies\/WorkOrderIssueAbilityStrategy.php.html#15\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy<\/a>"],[0,149,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#37\">App\\Services\\Appfolio\\AppfolioForServiceRequestService<\/a>"],[0,169,"<a href=\"Appfolio\/AppfolioService.php.html#36\">App\\Services\\Appfolio\\AppfolioService<\/a>"],[30,36,"<a href=\"AssigneeFilterService.php.html#40\">App\\Services\\AssigneeFilterService<\/a>"],[7.6923076923076925,7,"<a href=\"Aws\/LocationService.php.html#8\">App\\Services\\Aws\\LocationService<\/a>"],[100,17,"<a href=\"DateFilterService.php.html#27\">App\\Services\\DateFilterService<\/a>"],[59.696969696969695,134,"<a href=\"FilterService.php.html#43\">App\\Services\\FilterService<\/a>"],[0,7,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#11\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject<\/a>"],[0,72,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#43\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient<\/a>"],[0,6,"<a href=\"InvoiceRegister\/Pipes\/CreateOrUpdateInvoice.php.html#12\">App\\Services\\InvoiceRegister\\Pipes\\CreateOrUpdateInvoice<\/a>"],[0,1,"<a href=\"InvoiceRegister\/Pipes\/UpdateMaterials.php.html#10\">App\\Services\\InvoiceRegister\\Pipes\\UpdateMaterials<\/a>"],[0,76,"<a href=\"InvoiceRegister\/Pipes\/UpdateOrCreateInvoiceLineItems.php.html#28\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems<\/a>"],[0,1,"<a href=\"InvoiceRegister\/Pipes\/UpdateQuoteTasks.php.html#11\">App\\Services\\InvoiceRegister\\Pipes\\UpdateQuoteTasks<\/a>"],[0,1,"<a href=\"InvoiceRegister\/Pipes\/UpdateServiceCalls.php.html#11\">App\\Services\\InvoiceRegister\\Pipes\\UpdateServiceCalls<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Collections\/Appointments.php.html#15\">App\\Services\\Scheduling\\Domain\\Collections\\Appointments<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Collections\/Technicians.php.html#14\">App\\Services\\Scheduling\\Domain\\Collections\\Technicians<\/a>"],[0,4,"<a href=\"Scheduling\/Domain\/Collections\/WorkingHours.php.html#19\">App\\Services\\Scheduling\\Domain\\Collections\\WorkingHours<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/AvailableProviders.php.html#9\">App\\Services\\Scheduling\\Domain\\DTOs\\AvailableProviders<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/TaskSchedulingOptions.php.html#10\">App\\Services\\Scheduling\\Domain\\DTOs\\TaskSchedulingOptions<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/TechnicianList.php.html#9\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianList<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/TechnicianSchedule.php.html#9\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianSchedule<\/a>"],[0,4,"<a href=\"Scheduling\/Domain\/Entities\/AgendaSlot.php.html#7\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot<\/a>"],[0,4,"<a href=\"Scheduling\/Domain\/Entities\/Appointment.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment<\/a>"],[0,26,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#15\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda<\/a>"],[0,3,"<a href=\"Scheduling\/Domain\/Entities\/Location.php.html#9\">App\\Services\\Scheduling\\Domain\\Entities\\Location<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/RankedServiceWindow.php.html#5\">App\\Services\\Scheduling\\Domain\\Entities\\RankedServiceWindow<\/a>"],[0,11,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/Technician.php.html#9\">App\\Services\\Scheduling\\Domain\\Entities\\Technician<\/a>"],[0,22,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#17\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar<\/a>"],[0,3,"<a href=\"Scheduling\/Domain\/Entities\/WorkOrder.php.html#8\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrder<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/WorkOrderQuote.php.html#8\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrderQuote<\/a>"],[0,3,"<a href=\"Scheduling\/Domain\/Entities\/WorkingDay.php.html#12\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay<\/a>"],[100,0,"<a href=\"Scheduling\/Domain\/Exceptions\/AppointmentCollisionException.php.html#7\">App\\Services\\Scheduling\\Domain\\Exceptions\\AppointmentCollisionException<\/a>"],[100,0,"<a href=\"Scheduling\/Domain\/Exceptions\/InvalidAppointmentException.php.html#7\">App\\Services\\Scheduling\\Domain\\Exceptions\\InvalidAppointmentException<\/a>"],[0,11,"<a href=\"Scheduling\/SchedulingRepository.php.html#26\">App\\Services\\Scheduling\\SchedulingRepository<\/a>"],[0,35,"<a href=\"Scheduling\/SchedulingService.php.html#37\">App\\Services\\Scheduling\\SchedulingService<\/a>"],[0,1,"<a href=\"Scheduling\/Strategies\/EarliestStrategy.php.html#13\">App\\Services\\Scheduling\\Strategies\\EarliestStrategy<\/a>"],[0,1,"<a href=\"Scheduling\/Strategies\/EfficientStrategy.php.html#13\">App\\Services\\Scheduling\\Strategies\\EfficientStrategy<\/a>"],[0,1,"<a href=\"Scheduling\/Strategies\/EmergencyStrategy.php.html#12\">App\\Services\\Scheduling\\Strategies\\EmergencyStrategy<\/a>"],[36.92307692307693,7,"<a href=\"SearchService.php.html#30\">App\\Services\\SearchService<\/a>"],[0,2,"<a href=\"ServiceCall\/Trip\/Domain\/Exceptions\/TripStateServiceException.php.html#7\">App\\Services\\ServiceCall\\Trip\\Domain\\Exceptions\\TripStateServiceException<\/a>"],[0,4,"<a href=\"ServiceCall\/Trip\/EntityResolver.php.html#11\">App\\Services\\ServiceCall\\Trip\\EntityResolver<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Lula\/EnRouteHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Lula\\EnRouteHandler<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/EnRouteHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRouteHandler<\/a>"],[0,2,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/EnRoutePausedHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRoutePausedHandler<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/EndedHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EndedHandler<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/PausedHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\PausedHandler<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/WorkingHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\WorkingHandler<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/EnRouteHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRouteHandler<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/EnRoutePausedHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRoutePausedHandler<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/EndedHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EndedHandler<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/PausedHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\PausedHandler<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/WorkingHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\WorkingHandler<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/StateTransitionHandlerFactory.php.html#12\">App\\Services\\ServiceCall\\Trip\\StateTransitionHandlerFactory<\/a>"],[0,2,"<a href=\"ServiceCall\/Trip\/TripStateService.php.html#10\">App\\Services\\ServiceCall\\Trip\\TripStateService<\/a>"],[50,1,"<a href=\"ServiceCall\/Trip\/TripStateServiceProvider.php.html#7\">App\\Services\\ServiceCall\\Trip\\TripStateServiceProvider<\/a>"],[0,2,"<a href=\"ServiceRequestActivity\/Leaf\/InternalNotesLeaf.php.html#10\">App\\Services\\ServiceRequestActivity\\Leaf\\InternalNotesLeaf<\/a>"],[0,5,"<a href=\"ServiceRequestActivity\/Leaf\/StatusChangeLeaf.php.html#10\">App\\Services\\ServiceRequestActivity\\Leaf\\StatusChangeLeaf<\/a>"],[0,15,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#13\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport<\/a>"],[0,2,"<a href=\"ServiceRequestRegister\/Pipes\/PropertyRegister.php.html#13\">App\\Services\\ServiceRequestRegister\\Pipes\\PropertyRegister<\/a>"],[0,11,"<a href=\"ServiceRequestRegister\/Pipes\/ResidentRegister.php.html#11\">App\\Services\\ServiceRequestRegister\\Pipes\\ResidentRegister<\/a>"],[0,4,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestCategoryRegister.php.html#10\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestCategoryRegister<\/a>"],[0,13,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestPhotosRegister.php.html#16\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister<\/a>"],[0,10,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestRegister.php.html#17\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestRegister<\/a>"],[0,5,"<a href=\"ServiceRequestRegister\/ServiceRequestRegisterClient.php.html#25\">App\\Services\\ServiceRequestRegister\\ServiceRequestRegisterClient<\/a>"],[100,25,"<a href=\"SortService.php.html#34\">App\\Services\\SortService<\/a>"],[0,4,"<a href=\"Vendor\/Entities\/Configuration.php.html#5\">App\\Services\\Vendor\\Entities\\Configuration<\/a>"],[0,4,"<a href=\"Vendor\/Entities\/Organization.php.html#8\">App\\Services\\Vendor\\Entities\\Organization<\/a>"],[0,2,"<a href=\"Vendor\/Entities\/WorkOrder.php.html#7\">App\\Services\\Vendor\\Entities\\WorkOrder<\/a>"],[0,6,"<a href=\"Vendor\/Exceptions\/ServiceException.php.html#7\">App\\Services\\Vendor\\Exceptions\\ServiceException<\/a>"],[0,1,"<a href=\"Vendor\/Factory\/VendorServiceFactory.php.html#12\">App\\Services\\Vendor\\Factory\\VendorServiceFactory<\/a>"],[0,22,"<a href=\"Vendor\/Lula\/Authentication.php.html#15\">App\\Services\\Vendor\\Services\\Lula\\Authentication<\/a>"],[0,8,"<a href=\"Vendor\/Lula\/Config.php.html#8\">App\\Services\\Vendor\\Services\\Lula\\Config<\/a>"],[0,4,"<a href=\"Vendor\/Lula\/Exception\/AuthenticationException.php.html#7\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException<\/a>"],[0,8,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#7\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/LulaClient.php.html#10\">App\\Services\\Vendor\\Services\\Lula\\LulaClient<\/a>"],[0,36,"<a href=\"Vendor\/Lula\/LulaService.php.html#42\">App\\Services\\Vendor\\Services\\Lula\\LulaService<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Webhook\/LulaWebhookConfig.php.html#7\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookConfig<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/Webhook\/LulaWebhookSignatureValidator.php.html#10\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookSignatureValidator<\/a>"],[0,19,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#33\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService<\/a>"],[0,3,"<a href=\"Vendor\/VendorService.php.html#10\">App\\Services\\Vendor\\VendorService<\/a>"],[0,21,"<a href=\"VideoStream\/VideoStreamer.php.html#7\">App\\Services\\VideoStream\\VideoStreamer<\/a>"],[0,2,"<a href=\"Webhook\/WebhookClientProcessor.php.html#9\">App\\Services\\Webhook\\WebhookClientProcessor<\/a>"],[0,1,"<a href=\"Webhook\/WebhookRetryBackoffStrategy.php.html#7\">App\\Services\\Webhook\\WebhookRetryBackoffStrategy<\/a>"],[0,11,"<a href=\"WorkOrderActivity\/ActivityComposite.php.html#19\">App\\Services\\WorkOrderActivity\\ActivityComposite<\/a>"],[100,0,"<a href=\"WorkOrderActivity\/Exception\/InvalidActivityLogException.php.html#7\">App\\Services\\WorkOrderActivity\\Exception\\InvalidActivityLogException<\/a>"],[0,2,"<a href=\"WorkOrderActivity\/Leaf\/InternalNotesLeaf.php.html#9\">App\\Services\\WorkOrderActivity\\Leaf\\InternalNotesLeaf<\/a>"],[0,5,"<a href=\"WorkOrderActivity\/Leaf\/StatusChangeLeaf.php.html#10\">App\\Services\\WorkOrderActivity\\Leaf\\StatusChangeLeaf<\/a>"],[0,17,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#14\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport<\/a>"],[0,2,"<a href=\"WorkOrderRegister\/Pipes\/PropertyRegister.php.html#14\">App\\Services\\WorkOrderRegister\\Pipes\\PropertyRegister<\/a>"],[0,11,"<a href=\"WorkOrderRegister\/Pipes\/ResidentRegister.php.html#12\">App\\Services\\WorkOrderRegister\\Pipes\\ResidentRegister<\/a>"],[0,4,"<a href=\"WorkOrderRegister\/Pipes\/WorkOrderMediaRegister.php.html#10\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderMediaRegister<\/a>"],[0,11,"<a href=\"WorkOrderRegister\/Pipes\/WorkOrderRegister.php.html#13\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderRegister<\/a>"],[0,4,"<a href=\"WorkOrderRegister\/Pipes\/WorkOrderTaskRegister.php.html#11\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderTaskRegister<\/a>"],[0,12,"<a href=\"WorkOrderRegister\/WorkOrderRegisterClient.php.html#31\">App\\Services\\WorkOrderRegister\\WorkOrderRegisterClient<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Enums\/CarbonDayOfWeek.php.html#8\">App\\Services\\Scheduling\\Domain\\Enums\\CarbonDayOfWeek<\/a>"],[0,5,"<a href=\"Scheduling\/Domain\/Traits\/DistanceHelperTrait.php.html#7\">App\\Services\\Scheduling\\Domain\\Traits\\DistanceHelperTrait<\/a>"],[0,1,"<a href=\"Vendor\/Enum\/Service.php.html#9\">App\\Services\\Vendor\\Enum\\Service<\/a>"],[0,1,"<a href=\"Vendor\/Enum\/ServiceMode.php.html#5\">App\\Services\\Vendor\\Enum\\ServiceMode<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/Scope\/WorkOrderScope.php.html#8\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Ability\/AbilityResolver.php.html#17\">App\\Services\\Ability\\AbilityResolver::resolve<\/a>"],[0,3,"<a href=\"Ability\/Strategies\/IssueAbilityStrategy.php.html#18\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::getAbilities<\/a>"],[0,1,"<a href=\"Ability\/Strategies\/IssueAbilityStrategy.php.html#40\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::isDeleted<\/a>"],[0,3,"<a href=\"Ability\/Strategies\/IssueAbilityStrategy.php.html#51\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::filterDeleteAction<\/a>"],[0,4,"<a href=\"Ability\/Strategies\/IssueAbilityStrategy.php.html#66\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::filterCancelAction<\/a>"],[0,2,"<a href=\"Ability\/Strategies\/IssueAbilityStrategy.php.html#82\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::isMappedToActiveWorkOrder<\/a>"],[0,1,"<a href=\"Ability\/Strategies\/IssueAbilityStrategy.php.html#94\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::isClosedServiceRequest<\/a>"],[0,4,"<a href=\"Ability\/Strategies\/WorkOrderAbilityStrategy.php.html#18\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::getAbilities<\/a>"],[0,1,"<a href=\"Ability\/Strategies\/WorkOrderAbilityStrategy.php.html#43\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::abilitiesForWorkInProgressStatus<\/a>"],[0,3,"<a href=\"Ability\/Strategies\/WorkOrderAbilityStrategy.php.html#62\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::filterMobileAbilities<\/a>"],[0,3,"<a href=\"Ability\/Strategies\/WorkOrderAbilityStrategy.php.html#91\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::removeActions<\/a>"],[0,3,"<a href=\"Ability\/Strategies\/WorkOrderIssueAbilityStrategy.php.html#17\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::getAbilities<\/a>"],[0,1,"<a href=\"Ability\/Strategies\/WorkOrderIssueAbilityStrategy.php.html#38\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::isDeleted<\/a>"],[0,3,"<a href=\"Ability\/Strategies\/WorkOrderIssueAbilityStrategy.php.html#49\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::filterEditAction<\/a>"],[0,2,"<a href=\"Ability\/Strategies\/WorkOrderIssueAbilityStrategy.php.html#61\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::isMappedToClosedWorkOrder<\/a>"],[0,6,"<a href=\"Ability\/Strategies\/WorkOrderIssueAbilityStrategy.php.html#76\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::filterUnusedAbilitiesForMobile<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#93\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::__construct<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#108\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::get<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#132\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::post<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#152\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::patch<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#171\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::postAttachment<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#196\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::propertyDetails<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#222\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::unitDetails<\/a>"],[0,4,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#249\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getTenants<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#281\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::tenantDetails<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#305\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::ingestNewWorkOrdersToServiceRequests<\/a>"],[0,11,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#403\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::processNewWorkOrder<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#505\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::fetchAdditionalDataFromScrapper<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#568\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatServiceRequestPayload<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#619\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addSkippedWorkOrders<\/a>"],[0,4,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#639\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getPaginatedWorkOrders<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#671\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::updateWorkOrder<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#682\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getVendors<\/a>"],[0,14,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#710\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::processNewWorkOrders<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#808\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::checkIntegration<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#818\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::connect<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#835\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::responseFormatter<\/a>"],[0,18,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#857\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::logApiResponse<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#945\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getAppfolioWorkOrders<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#962\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getEntityListByUuid<\/a>"],[0,4,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#987\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::createNewServiceRequest<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1041\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addAppfolioApiWorkOrderLog<\/a>"],[0,18,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1069\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatPropertyInfo<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1129\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatTenantData<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1146\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatPhotos<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1165\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatTenantInput<\/a>"],[0,6,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1176\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addUnAssignedWorkOrders<\/a>"],[0,6,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1212\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addExistingWorkOrders<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioForServiceRequestService.php.html#1248\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addAppfolioIntegrationSkippedWorkOrders<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#92\">App\\Services\\Appfolio\\AppfolioService::__construct<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#107\">App\\Services\\Appfolio\\AppfolioService::get<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#131\">App\\Services\\Appfolio\\AppfolioService::post<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#151\">App\\Services\\Appfolio\\AppfolioService::patch<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioService.php.html#170\">App\\Services\\Appfolio\\AppfolioService::postAttachment<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioService.php.html#190\">App\\Services\\Appfolio\\AppfolioService::createNotes<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioService.php.html#219\">App\\Services\\Appfolio\\AppfolioService::propertyDetails<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioService.php.html#245\">App\\Services\\Appfolio\\AppfolioService::unitDetails<\/a>"],[0,4,"<a href=\"Appfolio\/AppfolioService.php.html#272\">App\\Services\\Appfolio\\AppfolioService::getTenants<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioService.php.html#304\">App\\Services\\Appfolio\\AppfolioService::tenantDetails<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioService.php.html#327\">App\\Services\\Appfolio\\AppfolioService::ingestNewWorkOrders<\/a>"],[0,19,"<a href=\"Appfolio\/AppfolioService.php.html#417\">App\\Services\\Appfolio\\AppfolioService::processNewWorkOrder<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioService.php.html#574\">App\\Services\\Appfolio\\AppfolioService::fetchAdditionalDataFromScrapper<\/a>"],[0,8,"<a href=\"Appfolio\/AppfolioService.php.html#637\">App\\Services\\Appfolio\\AppfolioService::formatWorkOrderPayload<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioService.php.html#687\">App\\Services\\Appfolio\\AppfolioService::addSkippedWorkOrders<\/a>"],[0,4,"<a href=\"Appfolio\/AppfolioService.php.html#707\">App\\Services\\Appfolio\\AppfolioService::getPaginatedWorkOrders<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#739\">App\\Services\\Appfolio\\AppfolioService::updateWorkOrder<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#750\">App\\Services\\Appfolio\\AppfolioService::getVendors<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#777\">App\\Services\\Appfolio\\AppfolioService::checkIntegration<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#787\">App\\Services\\Appfolio\\AppfolioService::connect<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioService.php.html#804\">App\\Services\\Appfolio\\AppfolioService::responseFormatter<\/a>"],[0,18,"<a href=\"Appfolio\/AppfolioService.php.html#826\">App\\Services\\Appfolio\\AppfolioService::logApiResponse<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#914\">App\\Services\\Appfolio\\AppfolioService::getWorkOrders<\/a>"],[0,13,"<a href=\"Appfolio\/AppfolioService.php.html#931\">App\\Services\\Appfolio\\AppfolioService::processNewWorkOrders<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#1022\">App\\Services\\Appfolio\\AppfolioService::getEntityListByUuid<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#1045\">App\\Services\\Appfolio\\AppfolioService::syncExistingWorkOrders<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#1088\">App\\Services\\Appfolio\\AppfolioService::validateAndUpdateJobs<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#1098\">App\\Services\\Appfolio\\AppfolioService::validatePropertyLocation<\/a>"],[0,4,"<a href=\"Appfolio\/AppfolioService.php.html#1111\">App\\Services\\Appfolio\\AppfolioService::requestNewWorkOrder<\/a>"],[0,2,"<a href=\"Appfolio\/AppfolioService.php.html#1165\">App\\Services\\Appfolio\\AppfolioService::addAppfolioApiWorkOrderLog<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#1191\">App\\Services\\Appfolio\\AppfolioService::findServiceCategory<\/a>"],[0,18,"<a href=\"Appfolio\/AppfolioService.php.html#1209\">App\\Services\\Appfolio\\AppfolioService::formatPropertyInfo<\/a>"],[0,5,"<a href=\"Appfolio\/AppfolioService.php.html#1269\">App\\Services\\Appfolio\\AppfolioService::formatTenantData<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#1286\">App\\Services\\Appfolio\\AppfolioService::formatPhotos<\/a>"],[0,3,"<a href=\"Appfolio\/AppfolioService.php.html#1305\">App\\Services\\Appfolio\\AppfolioService::formatTenantInput<\/a>"],[0,6,"<a href=\"Appfolio\/AppfolioService.php.html#1316\">App\\Services\\Appfolio\\AppfolioService::addUnAssignedWorkOrders<\/a>"],[0,6,"<a href=\"Appfolio\/AppfolioService.php.html#1352\">App\\Services\\Appfolio\\AppfolioService::addExistingWorkOrders<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioService.php.html#1388\">App\\Services\\Appfolio\\AppfolioService::addAppfolioIntegrationSkippedWorkOrders<\/a>"],[75,7,"<a href=\"AssigneeFilterService.php.html#56\">App\\Services\\AssigneeFilterService::applyAssigneeFilter<\/a>"],[0,6,"<a href=\"AssigneeFilterService.php.html#102\">App\\Services\\AssigneeFilterService::applyNotAssignedToFilter<\/a>"],[66.66666666666666,1,"<a href=\"AssigneeFilterService.php.html#175\">App\\Services\\AssigneeFilterService::applyAssignedToFilter<\/a>"],[43.47826086956522,2,"<a href=\"AssigneeFilterService.php.html#195\">App\\Services\\AssigneeFilterService::applyUnassignedFilter<\/a>"],[87.5,5,"<a href=\"AssigneeFilterService.php.html#233\">App\\Services\\AssigneeFilterService::applyWorkOrderAssigneeFilter<\/a>"],[0,5,"<a href=\"AssigneeFilterService.php.html#290\">App\\Services\\AssigneeFilterService::applyQuoteAssigneeFilter<\/a>"],[0,5,"<a href=\"AssigneeFilterService.php.html#344\">App\\Services\\AssigneeFilterService::applyServiceRequestAssigneeFilter<\/a>"],[0,1,"<a href=\"AssigneeFilterService.php.html#393\">App\\Services\\AssigneeFilterService::getWorkOrderIdsWithAssignees<\/a>"],[0,1,"<a href=\"AssigneeFilterService.php.html#406\">App\\Services\\AssigneeFilterService::getQuoteIdsWithAssignees<\/a>"],[0,1,"<a href=\"AssigneeFilterService.php.html#420\">App\\Services\\AssigneeFilterService::getServiceRequestIdsWithAssignees<\/a>"],[100,2,"<a href=\"AssigneeFilterService.php.html#438\">App\\Services\\AssigneeFilterService::resolveWhereClause<\/a>"],[100,1,"<a href=\"Aws\/LocationService.php.html#13\">App\\Services\\Aws\\LocationService::__construct<\/a>"],[0,2,"<a href=\"Aws\/LocationService.php.html#22\">App\\Services\\Aws\\LocationService::searchPlace<\/a>"],[0,2,"<a href=\"Aws\/LocationService.php.html#45\">App\\Services\\Aws\\LocationService::searchNearPosition<\/a>"],[0,2,"<a href=\"Aws\/LocationService.php.html#67\">App\\Services\\Aws\\LocationService::handleAwsException<\/a>"],[100,1,"<a href=\"DateFilterService.php.html#39\">App\\Services\\DateFilterService::applyDateFilter<\/a>"],[100,1,"<a href=\"DateFilterService.php.html#64\">App\\Services\\DateFilterService::resolveOperatorSetForDateSlug<\/a>"],[100,2,"<a href=\"DateFilterService.php.html#80\">App\\Services\\DateFilterService::dateValuesForIsOperation<\/a>"],[100,2,"<a href=\"DateFilterService.php.html#98\">App\\Services\\DateFilterService::dateValuesForIsNotOperation<\/a>"],[100,2,"<a href=\"DateFilterService.php.html#116\">App\\Services\\DateFilterService::dateValuesForIsAfterOperation<\/a>"],[100,1,"<a href=\"DateFilterService.php.html#132\">App\\Services\\DateFilterService::dateValuesForIsBeForeOperation<\/a>"],[100,2,"<a href=\"DateFilterService.php.html#149\">App\\Services\\DateFilterService::dateOperationResponse<\/a>"],[100,2,"<a href=\"DateFilterService.php.html#169\">App\\Services\\DateFilterService::getDateRangeFromSlug<\/a>"],[100,1,"<a href=\"DateFilterService.php.html#191\">App\\Services\\DateFilterService::applyBetweenFilter<\/a>"],[100,1,"<a href=\"DateFilterService.php.html#209\">App\\Services\\DateFilterService::applyNotBetweenFilter<\/a>"],[100,1,"<a href=\"DateFilterService.php.html#228\">App\\Services\\DateFilterService::applySingleDateFilter<\/a>"],[100,1,"<a href=\"DateFilterService.php.html#241\">App\\Services\\DateFilterService::resolveWhereClauseOperator<\/a>"],[100,1,"<a href=\"FilterService.php.html#78\">App\\Services\\FilterService::__construct<\/a>"],[100,2,"<a href=\"FilterService.php.html#93\">App\\Services\\FilterService::registerFilterHandler<\/a>"],[100,3,"<a href=\"FilterService.php.html#109\">App\\Services\\FilterService::getFilterHandler<\/a>"],[80,5,"<a href=\"FilterService.php.html#173\">App\\Services\\FilterService::filterQuery<\/a>"],[100,1,"<a href=\"FilterService.php.html#195\">App\\Services\\FilterService::findFilterColumn<\/a>"],[50,3,"<a href=\"FilterService.php.html#220\">App\\Services\\FilterService::generateQuery<\/a>"],[62.5,1,"<a href=\"FilterService.php.html#257\">App\\Services\\FilterService::resolveWhereClauseOperator<\/a>"],[76.47058823529412,3,"<a href=\"FilterService.php.html#272\">App\\Services\\FilterService::resolveWhereClause<\/a>"],[90.9090909090909,6,"<a href=\"FilterService.php.html#331\">App\\Services\\FilterService::processQuery<\/a>"],[37.5,1,"<a href=\"FilterService.php.html#369\">App\\Services\\FilterService::applyEntityIdFilter<\/a>"],[73.33333333333333,1,"<a href=\"FilterService.php.html#393\">App\\Services\\FilterService::registerDefaultFilterHandlers<\/a>"],[100,3,"<a href=\"FilterService.php.html#422\">App\\Services\\FilterService::hasFilters<\/a>"],[100,3,"<a href=\"FilterService.php.html#432\">App\\Services\\FilterService::isDirectFilter<\/a>"],[100,2,"<a href=\"FilterService.php.html#442\">App\\Services\\FilterService::isFilterGroup<\/a>"],[90.9090909090909,2,"<a href=\"FilterService.php.html#453\">App\\Services\\FilterService::applyDirectFilter<\/a>"],[92.85714285714286,3,"<a href=\"FilterService.php.html#478\">App\\Services\\FilterService::applyFilterGroup<\/a>"],[38.46153846153847,1,"<a href=\"FilterService.php.html#504\">App\\Services\\FilterService::findWorkOrderFilterColumn<\/a>"],[45.45454545454545,1,"<a href=\"FilterService.php.html#524\">App\\Services\\FilterService::findVendorWorkOrderFilterColumn<\/a>"],[71.42857142857143,1,"<a href=\"FilterService.php.html#542\">App\\Services\\FilterService::findServiceRequestFilterColumn<\/a>"],[100,1,"<a href=\"FilterService.php.html#556\">App\\Services\\FilterService::findUserFilterColumn<\/a>"],[50,1,"<a href=\"FilterService.php.html#568\">App\\Services\\FilterService::findQuoteFilterColumn<\/a>"],[0,11,"<a href=\"FilterService.php.html#595\">App\\Services\\FilterService::generateWorkOrderQuery<\/a>"],[0,10,"<a href=\"FilterService.php.html#638\">App\\Services\\FilterService::generateVendorWorkOrderQuery<\/a>"],[0,6,"<a href=\"FilterService.php.html#674\">App\\Services\\FilterService::generateServiceRequestQuery<\/a>"],[0,4,"<a href=\"FilterService.php.html#706\">App\\Services\\FilterService::generateUserQuery<\/a>"],[0,9,"<a href=\"FilterService.php.html#735\">App\\Services\\FilterService::generateQuoteQuery<\/a>"],[100,7,"<a href=\"FilterService.php.html#763\">App\\Services\\FilterService::resolveWorkOrderWhereClauseOperator<\/a>"],[100,7,"<a href=\"FilterService.php.html#779\">App\\Services\\FilterService::resolveVendorWorkOrderWhereClauseOperator<\/a>"],[100,7,"<a href=\"FilterService.php.html#795\">App\\Services\\FilterService::resolveServiceRequestWhereClauseOperator<\/a>"],[100,5,"<a href=\"FilterService.php.html#811\">App\\Services\\FilterService::resolveUserWhereClauseOperator<\/a>"],[100,7,"<a href=\"FilterService.php.html#825\">App\\Services\\FilterService::resolveQuoteWhereClauseOperator<\/a>"],[100,1,"<a href=\"FilterService.php.html#846\">App\\Services\\FilterService::applySimpleFilter<\/a>"],[100,1,"<a href=\"FilterService.php.html#866\">App\\Services\\FilterService::applyUuidFilter<\/a>"],[100,1,"<a href=\"FilterService.php.html#885\">App\\Services\\FilterService::applyDateFilter<\/a>"],[100,7,"<a href=\"FilterService.php.html#897\">App\\Services\\FilterService::getFilterTypeForField<\/a>"],[71.42857142857143,1,"<a href=\"FilterService.php.html#939\">App\\Services\\FilterService::getModelForField<\/a>"],[0,3,"<a href=\"FilterService.php.html#958\">App\\Services\\FilterService::applySubmittedByFilter<\/a>"],[0,2,"<a href=\"FilterService.php.html#984\">App\\Services\\FilterService::applyTagWorkOrderIdFilter<\/a>"],[0,1,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#13\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject::__construct<\/a>"],[0,1,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#21\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject::getRequest<\/a>"],[0,1,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#26\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject::getUser<\/a>"],[0,1,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#31\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject::getWorkOrder<\/a>"],[0,1,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#36\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject::getWorkOrderTask<\/a>"],[0,1,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#41\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject::setInvoice<\/a>"],[0,1,"<a href=\"InvoiceRegister\/DTO\/InvoiceRegisterDataObject.php.html#46\">App\\Services\\InvoiceRegister\\DTO\\InvoiceRegisterDataObject::getInvoice<\/a>"],[0,11,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#45\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient::register<\/a>"],[0,25,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#116\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient::generateInvoiceDetailsFromWorkOrderServiceCalls<\/a>"],[0,7,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#283\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient::generateInvoiceDetailsFromVendorExternalInvoiceDetails<\/a>"],[0,8,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#425\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient::getQuoteDetails<\/a>"],[0,13,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#507\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient::getHourlyDetails<\/a>"],[0,3,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#627\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient::getNoWorkDetails<\/a>"],[0,5,"<a href=\"InvoiceRegister\/InvoiceRegisterClient.php.html#653\">App\\Services\\InvoiceRegister\\InvoiceRegisterClient::tripStatusLabel<\/a>"],[0,6,"<a href=\"InvoiceRegister\/Pipes\/CreateOrUpdateInvoice.php.html#14\">App\\Services\\InvoiceRegister\\Pipes\\CreateOrUpdateInvoice::handle<\/a>"],[0,1,"<a href=\"InvoiceRegister\/Pipes\/UpdateMaterials.php.html#12\">App\\Services\\InvoiceRegister\\Pipes\\UpdateMaterials::handle<\/a>"],[0,40,"<a href=\"InvoiceRegister\/Pipes\/UpdateOrCreateInvoiceLineItems.php.html#30\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems::handle<\/a>"],[0,28,"<a href=\"InvoiceRegister\/Pipes\/UpdateOrCreateInvoiceLineItems.php.html#262\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems::createOrUpdateSubsidiaries<\/a>"],[0,8,"<a href=\"InvoiceRegister\/Pipes\/UpdateOrCreateInvoiceLineItems.php.html#370\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems::validateMedia<\/a>"],[0,1,"<a href=\"InvoiceRegister\/Pipes\/UpdateQuoteTasks.php.html#13\">App\\Services\\InvoiceRegister\\Pipes\\UpdateQuoteTasks::handle<\/a>"],[0,1,"<a href=\"InvoiceRegister\/Pipes\/UpdateServiceCalls.php.html#13\">App\\Services\\InvoiceRegister\\Pipes\\UpdateServiceCalls::handle<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Collections\/Appointments.php.html#20\">App\\Services\\Scheduling\\Domain\\Collections\\Appointments::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Collections\/Appointments.php.html#25\">App\\Services\\Scheduling\\Domain\\Collections\\Appointments::from<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Collections\/Technicians.php.html#19\">App\\Services\\Scheduling\\Domain\\Collections\\Technicians::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Collections\/Technicians.php.html#27\">App\\Services\\Scheduling\\Domain\\Collections\\Technicians::from<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Collections\/WorkingHours.php.html#24\">App\\Services\\Scheduling\\Domain\\Collections\\WorkingHours::__construct<\/a>"],[0,3,"<a href=\"Scheduling\/Domain\/Collections\/WorkingHours.php.html#29\">App\\Services\\Scheduling\\Domain\\Collections\\WorkingHours::from<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/AvailableProviders.php.html#15\">App\\Services\\Scheduling\\Domain\\DTOs\\AvailableProviders::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/TaskSchedulingOptions.php.html#16\">App\\Services\\Scheduling\\Domain\\DTOs\\TaskSchedulingOptions::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/TechnicianList.php.html#16\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianList::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/DTOs\/TechnicianSchedule.php.html#15\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianSchedule::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/AgendaSlot.php.html#9\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/AgendaSlot.php.html#11\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::getStartTime<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/AgendaSlot.php.html#16\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::getEndTime<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/AgendaSlot.php.html#21\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::getAppointment<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/Appointment.php.html#12\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment::__construct<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/Appointment.php.html#20\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment::from<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/Appointment.php.html#39\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment::is<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#27\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::__construct<\/a>"],[0,9,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#45\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::setAppointment<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#119\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getOpenSlots<\/a>"],[0,4,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#131\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getSlotsByAppointment<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#149\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getOccupiedSlots<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#154\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getAgendaDate<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#163\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::findAndAppendTimeIntervals<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#180\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getAgendaSlotByTime<\/a>"],[0,6,"<a href=\"Scheduling\/Domain\/Entities\/DailyAgenda.php.html#198\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getRequiredTimeIntervals<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/Location.php.html#11\">App\\Services\\Scheduling\\Domain\\Entities\\Location::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/Location.php.html#13\">App\\Services\\Scheduling\\Domain\\Entities\\Location::fromProperty<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/Location.php.html#18\">App\\Services\\Scheduling\\Domain\\Entities\\Location::fromTechnician<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/RankedServiceWindow.php.html#7\">App\\Services\\Scheduling\\Domain\\Entities\\RankedServiceWindow::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#18\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::__construct<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#28\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::fromReference<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#45\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getResidentStartTime<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#50\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getResidentEndTime<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#55\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getProviderStartTime<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#60\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getProviderEndTime<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#65\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::setEndTime<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#70\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::setDestination<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/ServiceWindow.php.html#83\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::encodedReference<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/Technician.php.html#11\">App\\Services\\Scheduling\\Domain\\Entities\\Technician::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/Technician.php.html#22\">App\\Services\\Scheduling\\Domain\\Entities\\Technician::from<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#25\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::__construct<\/a>"],[0,3,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#55\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::isAvailableServiceWindow<\/a>"],[0,10,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#79\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getOpenServiceWindows<\/a>"],[0,4,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#163\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::applyAppointments<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#194\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getOccupiedDays<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#204\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getFreeSlots<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/TechnicianCalendar.php.html#211\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getNearestAppointment<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/WorkOrder.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrder::__construct<\/a>"],[0,2,"<a href=\"Scheduling\/Domain\/Entities\/WorkOrder.php.html#27\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrder::from<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/WorkOrderQuote.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrderQuote::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/WorkOrderQuote.php.html#12\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrderQuote::from<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/WorkingDay.php.html#14\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay::__construct<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/WorkingDay.php.html#19\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay::from<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Entities\/WorkingDay.php.html#28\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay::is<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingRepository.php.html#31\">App\\Services\\Scheduling\\SchedulingRepository::getWorkOrder<\/a>"],[0,2,"<a href=\"Scheduling\/SchedulingRepository.php.html#49\">App\\Services\\Scheduling\\SchedulingRepository::getTechniciansForTask<\/a>"],[0,2,"<a href=\"Scheduling\/SchedulingRepository.php.html#108\">App\\Services\\Scheduling\\SchedulingRepository::getTechnicianByUUID<\/a>"],[0,6,"<a href=\"Scheduling\/SchedulingRepository.php.html#169\">App\\Services\\Scheduling\\SchedulingRepository::registerServiceCall<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#41\">App\\Services\\Scheduling\\SchedulingService::__construct<\/a>"],[0,3,"<a href=\"Scheduling\/SchedulingService.php.html#43\">App\\Services\\Scheduling\\SchedulingService::getTaskSchedulingOptions<\/a>"],[0,4,"<a href=\"Scheduling\/SchedulingService.php.html#104\">App\\Services\\Scheduling\\SchedulingService::getTechnicianList<\/a>"],[0,2,"<a href=\"Scheduling\/SchedulingService.php.html#173\">App\\Services\\Scheduling\\SchedulingService::getTechnicianSchedules<\/a>"],[0,3,"<a href=\"Scheduling\/SchedulingService.php.html#225\">App\\Services\\Scheduling\\SchedulingService::registerServiceCall<\/a>"],[0,5,"<a href=\"Scheduling\/SchedulingService.php.html#275\">App\\Services\\Scheduling\\SchedulingService::findStartAndEndTimes<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#305\">App\\Services\\Scheduling\\SchedulingService::getVendors<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#313\">App\\Services\\Scheduling\\SchedulingService::getScheduler<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#322\">App\\Services\\Scheduling\\SchedulingService::getEstimatedDuration<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#327\">App\\Services\\Scheduling\\SchedulingService::getCurrentDuration<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#332\">App\\Services\\Scheduling\\SchedulingService::getCurrentMode<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#337\">App\\Services\\Scheduling\\SchedulingService::getCurrentMethod<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#342\">App\\Services\\Scheduling\\SchedulingService::getCurrentWorkPerform<\/a>"],[0,2,"<a href=\"Scheduling\/SchedulingService.php.html#350\">App\\Services\\Scheduling\\SchedulingService::getCurrentLinkedQuote<\/a>"],[0,1,"<a href=\"Scheduling\/SchedulingService.php.html#365\">App\\Services\\Scheduling\\SchedulingService::getQualifiedTechnicians<\/a>"],[0,7,"<a href=\"Scheduling\/SchedulingService.php.html#391\">App\\Services\\Scheduling\\SchedulingService::filterWindowsByResidentAvailability<\/a>"],[0,1,"<a href=\"Scheduling\/Strategies\/EarliestStrategy.php.html#19\">App\\Services\\Scheduling\\Strategies\\EarliestStrategy::getPreferredWindows<\/a>"],[0,1,"<a href=\"Scheduling\/Strategies\/EfficientStrategy.php.html#19\">App\\Services\\Scheduling\\Strategies\\EfficientStrategy::getPreferredWindows<\/a>"],[0,1,"<a href=\"Scheduling\/Strategies\/EmergencyStrategy.php.html#18\">App\\Services\\Scheduling\\Strategies\\EmergencyStrategy::getPreferredWindows<\/a>"],[100,1,"<a href=\"SearchService.php.html#42\">App\\Services\\SearchService::__construct<\/a>"],[100,1,"<a href=\"SearchService.php.html#53\">App\\Services\\SearchService::registerSearchHandler<\/a>"],[100,1,"<a href=\"SearchService.php.html#64\">App\\Services\\SearchService::getSearchHandler<\/a>"],[83.33333333333334,3,"<a href=\"SearchService.php.html#79\">App\\Services\\SearchService::applySearch<\/a>"],[28.57142857142857,1,"<a href=\"SearchService.php.html#101\">App\\Services\\SearchService::registerDefaultSearchHandlers<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Domain\/Exceptions\/TripStateServiceException.php.html#9\">App\\Services\\ServiceCall\\Trip\\Domain\\Exceptions\\TripStateServiceException::invalidEntityResolver<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Domain\/Exceptions\/TripStateServiceException.php.html#14\">App\\Services\\ServiceCall\\Trip\\Domain\\Exceptions\\TripStateServiceException::invalidStateTransitionArgumentException<\/a>"],[0,4,"<a href=\"ServiceCall\/Trip\/EntityResolver.php.html#20\">App\\Services\\ServiceCall\\Trip\\EntityResolver::resolve<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Lula\/EnRouteHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Lula\\EnRouteHandler::apply<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/EnRouteHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRouteHandler::apply<\/a>"],[0,2,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/EnRoutePausedHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRoutePausedHandler::apply<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/EndedHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EndedHandler::apply<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/PausedHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\PausedHandler::apply<\/a>"],[0,3,"<a href=\"ServiceCall\/Trip\/Handlers\/Technician\/WorkingHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\WorkingHandler::apply<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/EnRouteHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRouteHandler::apply<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/EnRoutePausedHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRoutePausedHandler::apply<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/EndedHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EndedHandler::apply<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/PausedHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\PausedHandler::apply<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/Handlers\/Vendor\/WorkingHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\WorkingHandler::apply<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/StateTransitionHandlerFactory.php.html#16\">App\\Services\\ServiceCall\\Trip\\StateTransitionHandlerFactory::__construct<\/a>"],[0,2,"<a href=\"ServiceCall\/Trip\/StateTransitionHandlerFactory.php.html#27\">App\\Services\\ServiceCall\\Trip\\StateTransitionHandlerFactory::make<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/TripStateService.php.html#15\">App\\Services\\ServiceCall\\Trip\\TripStateService::__construct<\/a>"],[0,1,"<a href=\"ServiceCall\/Trip\/TripStateService.php.html#24\">App\\Services\\ServiceCall\\Trip\\TripStateService::updateTripState<\/a>"],[50,1,"<a href=\"ServiceCall\/Trip\/TripStateServiceProvider.php.html#9\">App\\Services\\ServiceCall\\Trip\\TripStateServiceProvider::register<\/a>"],[0,1,"<a href=\"ServiceRequestActivity\/Leaf\/InternalNotesLeaf.php.html#12\">App\\Services\\ServiceRequestActivity\\Leaf\\InternalNotesLeaf::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestActivity\/Leaf\/InternalNotesLeaf.php.html#17\">App\\Services\\ServiceRequestActivity\\Leaf\\InternalNotesLeaf::getActivities<\/a>"],[0,1,"<a href=\"ServiceRequestActivity\/Leaf\/StatusChangeLeaf.php.html#12\">App\\Services\\ServiceRequestActivity\\Leaf\\StatusChangeLeaf::__construct<\/a>"],[0,4,"<a href=\"ServiceRequestActivity\/Leaf\/StatusChangeLeaf.php.html#17\">App\\Services\\ServiceRequestActivity\\Leaf\\StatusChangeLeaf::getActivities<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#15\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#25\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::setRequest<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#30\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::getRequest<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#35\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::setOrganization<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#40\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::getOrganization<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#45\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::setUser<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#50\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::getUser<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#55\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::setTimezone<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#60\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::getTimezone<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#65\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::setProperty<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#70\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::getProperty<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#75\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::setResident<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#80\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::getResident<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#85\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::setServiceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestRegister\/Dto\/ServiceRequestRegisterTransport.php.html#90\">App\\Services\\ServiceRequestRegister\\Dto\\ServiceRequestRegisterTransport::getServiceRequest<\/a>"],[0,2,"<a href=\"ServiceRequestRegister\/Pipes\/PropertyRegister.php.html#15\">App\\Services\\ServiceRequestRegister\\Pipes\\PropertyRegister::handle<\/a>"],[0,11,"<a href=\"ServiceRequestRegister\/Pipes\/ResidentRegister.php.html#13\">App\\Services\\ServiceRequestRegister\\Pipes\\ResidentRegister::handle<\/a>"],[0,4,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestCategoryRegister.php.html#12\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestCategoryRegister::handle<\/a>"],[0,5,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestPhotosRegister.php.html#18\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::handle<\/a>"],[0,2,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestPhotosRegister.php.html#41\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::isAppfolioSource<\/a>"],[0,3,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestPhotosRegister.php.html#48\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::dispatchPhotoJobs<\/a>"],[0,3,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestPhotosRegister.php.html#67\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::updateAdditionalInfo<\/a>"],[0,10,"<a href=\"ServiceRequestRegister\/Pipes\/ServiceRequestRegister.php.html#19\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestRegister::handle<\/a>"],[0,5,"<a href=\"ServiceRequestRegister\/ServiceRequestRegisterClient.php.html#27\">App\\Services\\ServiceRequestRegister\\ServiceRequestRegisterClient::register<\/a>"],[100,1,"<a href=\"SortService.php.html#67\">App\\Services\\SortService::__construct<\/a>"],[100,2,"<a href=\"SortService.php.html#95\">App\\Services\\SortService::applySorting<\/a>"],[100,16,"<a href=\"SortService.php.html#131\">App\\Services\\SortService::prepareSortValue<\/a>"],[100,2,"<a href=\"SortService.php.html#180\">App\\Services\\SortService::registerSortFieldMappings<\/a>"],[100,1,"<a href=\"SortService.php.html#198\">App\\Services\\SortService::getSortFieldMappings<\/a>"],[100,1,"<a href=\"SortService.php.html#209\">App\\Services\\SortService::setDefaultSortField<\/a>"],[100,1,"<a href=\"SortService.php.html#220\">App\\Services\\SortService::getDefaultSortField<\/a>"],[100,1,"<a href=\"SortService.php.html#235\">App\\Services\\SortService::registerDefaultSortMappings<\/a>"],[0,1,"<a href=\"Vendor\/Entities\/Configuration.php.html#7\">App\\Services\\Vendor\\Entities\\Configuration::__construct<\/a>"],[0,1,"<a href=\"Vendor\/Entities\/Configuration.php.html#15\">App\\Services\\Vendor\\Entities\\Configuration::from<\/a>"],[0,1,"<a href=\"Vendor\/Entities\/Configuration.php.html#23\">App\\Services\\Vendor\\Entities\\Configuration::getClientId<\/a>"],[0,1,"<a href=\"Vendor\/Entities\/Configuration.php.html#28\">App\\Services\\Vendor\\Entities\\Configuration::getSecretKey<\/a>"],[0,1,"<a href=\"Vendor\/Entities\/Organization.php.html#10\">App\\Services\\Vendor\\Entities\\Organization::__construct<\/a>"],[0,3,"<a href=\"Vendor\/Entities\/Organization.php.html#12\">App\\Services\\Vendor\\Entities\\Organization::from<\/a>"],[0,1,"<a href=\"Vendor\/Entities\/WorkOrder.php.html#9\">App\\Services\\Vendor\\Entities\\WorkOrder::__construct<\/a>"],[0,1,"<a href=\"Vendor\/Entities\/WorkOrder.php.html#11\">App\\Services\\Vendor\\Entities\\WorkOrder::from<\/a>"],[0,1,"<a href=\"Vendor\/Exceptions\/ServiceException.php.html#9\">App\\Services\\Vendor\\Exceptions\\ServiceException::invalidService<\/a>"],[0,1,"<a href=\"Vendor\/Exceptions\/ServiceException.php.html#14\">App\\Services\\Vendor\\Exceptions\\ServiceException::tokenExpired<\/a>"],[0,1,"<a href=\"Vendor\/Exceptions\/ServiceException.php.html#19\">App\\Services\\Vendor\\Exceptions\\ServiceException::invalidToken<\/a>"],[0,1,"<a href=\"Vendor\/Exceptions\/ServiceException.php.html#24\">App\\Services\\Vendor\\Exceptions\\ServiceException::invalidCacheKey<\/a>"],[0,1,"<a href=\"Vendor\/Exceptions\/ServiceException.php.html#29\">App\\Services\\Vendor\\Exceptions\\ServiceException::invalidKey<\/a>"],[0,1,"<a href=\"Vendor\/Exceptions\/ServiceException.php.html#34\">App\\Services\\Vendor\\Exceptions\\ServiceException::invalidVendor<\/a>"],[0,1,"<a href=\"Vendor\/Factory\/VendorServiceFactory.php.html#17\">App\\Services\\Vendor\\Factory\\VendorServiceFactory::provider<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/Authentication.php.html#38\">App\\Services\\Vendor\\Services\\Lula\\Authentication::authenticate<\/a>"],[0,2,"<a href=\"Vendor\/Lula\/Authentication.php.html#55\">App\\Services\\Vendor\\Services\\Lula\\Authentication::getToken<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Authentication.php.html#65\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setClientId<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Authentication.php.html#72\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setClientKey<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Authentication.php.html#83\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setScopes<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Authentication.php.html#90\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setCacheKey<\/a>"],[0,4,"<a href=\"Vendor\/Lula\/Authentication.php.html#97\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setOrganization<\/a>"],[0,2,"<a href=\"Vendor\/Lula\/Authentication.php.html#116\">App\\Services\\Vendor\\Services\\Lula\\Authentication::invalidateToken<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/Authentication.php.html#131\">App\\Services\\Vendor\\Services\\Lula\\Authentication::attempt<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Authentication.php.html#162\">App\\Services\\Vendor\\Services\\Lula\\Authentication::getAuthorizationToken<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Authentication.php.html#167\">App\\Services\\Vendor\\Services\\Lula\\Authentication::getCacheKey<\/a>"],[0,2,"<a href=\"Vendor\/Lula\/Authentication.php.html#172\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setToken<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Config.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Config::__construct<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/Config.php.html#24\">App\\Services\\Vendor\\Services\\Lula\\Config::getInstance<\/a>"],[0,2,"<a href=\"Vendor\/Lula\/Config.php.html#36\">App\\Services\\Vendor\\Services\\Lula\\Config::baseUrl<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Config.php.html#49\">App\\Services\\Vendor\\Services\\Lula\\Config::version<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Config.php.html#54\">App\\Services\\Vendor\\Services\\Lula\\Config::getEnvironmentBaseUrl<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/AuthenticationException.php.html#9\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::invalidClient<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/AuthenticationException.php.html#14\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::invalidClientKey<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/AuthenticationException.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::invalidRequest<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/AuthenticationException.php.html#24\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::accessTokenMissing<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#9\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::emptyWorkOrderTask<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#14\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::serviceCallRegisterFailed<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::failedResponse<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#24\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::lulaAppointmentRegisterFailed<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#29\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::lulaWorkOrder<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#34\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::unexpectedResponseData<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#39\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::emptyVendor<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Exception\/WorkOrderSendException.php.html#44\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::vendorAppointmentRegisterFailed<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaClient.php.html#12\">App\\Services\\Vendor\\Services\\Lula\\LulaClient::getInstance<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaClient.php.html#22\">App\\Services\\Vendor\\Services\\Lula\\LulaClient::send<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaClient.php.html#36\">App\\Services\\Vendor\\Services\\Lula\\LulaClient::__callStatic<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaService.php.html#50\">App\\Services\\Vendor\\Services\\Lula\\LulaService::__construct<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaService.php.html#55\">App\\Services\\Vendor\\Services\\Lula\\LulaService::authenticate<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaService.php.html#62\">App\\Services\\Vendor\\Services\\Lula\\LulaService::isAuthenticate<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaService.php.html#67\">App\\Services\\Vendor\\Services\\Lula\\LulaService::token<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/LulaService.php.html#72\">App\\Services\\Vendor\\Services\\Lula\\LulaService::getClient<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaService.php.html#92\">App\\Services\\Vendor\\Services\\Lula\\LulaService::setOrganization<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaService.php.html#104\">App\\Services\\Vendor\\Services\\Lula\\LulaService::setMode<\/a>"],[0,15,"<a href=\"Vendor\/Lula\/LulaService.php.html#118\">App\\Services\\Vendor\\Services\\Lula\\LulaService::sendWorkOrder<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/LulaService.php.html#319\">App\\Services\\Vendor\\Services\\Lula\\LulaService::workOrderList<\/a>"],[0,6,"<a href=\"Vendor\/Lula\/LulaService.php.html#329\">App\\Services\\Vendor\\Services\\Lula\\LulaService::getWorkOrder<\/a>"],[0,5,"<a href=\"Vendor\/Lula\/LulaService.php.html#376\">App\\Services\\Vendor\\Services\\Lula\\LulaService::updateWorkOrder<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Webhook\/LulaWebhookConfig.php.html#16\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookConfig::__construct<\/a>"],[0,3,"<a href=\"Vendor\/Lula\/Webhook\/LulaWebhookSignatureValidator.php.html#12\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookSignatureValidator::isValid<\/a>"],[0,1,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#35\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::authenticate<\/a>"],[0,1,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#42\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::token<\/a>"],[0,12,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#52\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::sendWorkOrder<\/a>"],[0,1,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#203\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::setOrganization<\/a>"],[0,1,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#209\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::getClient<\/a>"],[0,1,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#216\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::setMode<\/a>"],[0,1,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#224\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::getWorkOrder<\/a>"],[0,1,"<a href=\"Vendor\/ThirdParty\/ThirdPartyService.php.html#232\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::updateWorkOrder<\/a>"],[0,1,"<a href=\"Vendor\/VendorService.php.html#17\">App\\Services\\Vendor\\VendorService::__construct<\/a>"],[0,1,"<a href=\"Vendor\/VendorService.php.html#23\">App\\Services\\Vendor\\VendorService::make<\/a>"],[0,1,"<a href=\"Vendor\/VendorService.php.html#28\">App\\Services\\Vendor\\VendorService::getInstance<\/a>"],[0,1,"<a href=\"VideoStream\/VideoStreamer.php.html#16\">App\\Services\\VideoStream\\VideoStreamer::__construct<\/a>"],[0,1,"<a href=\"VideoStream\/VideoStreamer.php.html#24\">App\\Services\\VideoStream\\VideoStreamer::start<\/a>"],[0,2,"<a href=\"VideoStream\/VideoStreamer.php.html#35\">App\\Services\\VideoStream\\VideoStreamer::open<\/a>"],[0,12,"<a href=\"VideoStream\/VideoStreamer.php.html#47\">App\\Services\\VideoStream\\VideoStreamer::setHeader<\/a>"],[0,1,"<a href=\"VideoStream\/VideoStreamer.php.html#109\">App\\Services\\VideoStream\\VideoStreamer::end<\/a>"],[0,4,"<a href=\"VideoStream\/VideoStreamer.php.html#118\">App\\Services\\VideoStream\\VideoStreamer::stream<\/a>"],[0,2,"<a href=\"Webhook\/WebhookClientProcessor.php.html#11\">App\\Services\\Webhook\\WebhookClientProcessor::processWebhook<\/a>"],[0,1,"<a href=\"Webhook\/WebhookRetryBackoffStrategy.php.html#9\">App\\Services\\Webhook\\WebhookRetryBackoffStrategy::waitInSecondsAfterAttempt<\/a>"],[0,2,"<a href=\"WorkOrderActivity\/ActivityComposite.php.html#30\">App\\Services\\WorkOrderActivity\\ActivityComposite::addActivities<\/a>"],[0,3,"<a href=\"WorkOrderActivity\/ActivityComposite.php.html#39\">App\\Services\\WorkOrderActivity\\ActivityComposite::getActivities<\/a>"],[0,4,"<a href=\"WorkOrderActivity\/ActivityComposite.php.html#58\">App\\Services\\WorkOrderActivity\\ActivityComposite::paginate<\/a>"],[0,1,"<a href=\"WorkOrderActivity\/ActivityComposite.php.html#69\">App\\Services\\WorkOrderActivity\\ActivityComposite::getActivityComponent<\/a>"],[0,1,"<a href=\"WorkOrderActivity\/ActivityComposite.php.html#80\">App\\Services\\WorkOrderActivity\\ActivityComposite::addActivityComponent<\/a>"],[0,1,"<a href=\"WorkOrderActivity\/Leaf\/InternalNotesLeaf.php.html#11\">App\\Services\\WorkOrderActivity\\Leaf\\InternalNotesLeaf::__construct<\/a>"],[0,1,"<a href=\"WorkOrderActivity\/Leaf\/InternalNotesLeaf.php.html#16\">App\\Services\\WorkOrderActivity\\Leaf\\InternalNotesLeaf::getActivities<\/a>"],[0,1,"<a href=\"WorkOrderActivity\/Leaf\/StatusChangeLeaf.php.html#12\">App\\Services\\WorkOrderActivity\\Leaf\\StatusChangeLeaf::__construct<\/a>"],[0,4,"<a href=\"WorkOrderActivity\/Leaf\/StatusChangeLeaf.php.html#17\">App\\Services\\WorkOrderActivity\\Leaf\\StatusChangeLeaf::getActivities<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#16\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::__construct<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#27\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setRequest<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#32\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getRequest<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#37\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setOrganization<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#42\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getOrganization<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#47\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setUser<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#52\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getUser<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#57\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setTimezone<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#62\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getTimezone<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#67\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setProperty<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#72\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getProperty<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#77\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setResident<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#82\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getResident<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#87\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#92\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#97\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setServiceRequest<\/a>"],[0,1,"<a href=\"WorkOrderRegister\/DTO\/WorkOrderRegisterTransport.php.html#102\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getServiceRequest<\/a>"],[0,2,"<a href=\"WorkOrderRegister\/Pipes\/PropertyRegister.php.html#20\">App\\Services\\WorkOrderRegister\\Pipes\\PropertyRegister::handle<\/a>"],[0,11,"<a href=\"WorkOrderRegister\/Pipes\/ResidentRegister.php.html#14\">App\\Services\\WorkOrderRegister\\Pipes\\ResidentRegister::handle<\/a>"],[0,4,"<a href=\"WorkOrderRegister\/Pipes\/WorkOrderMediaRegister.php.html#12\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderMediaRegister::handle<\/a>"],[0,11,"<a href=\"WorkOrderRegister\/Pipes\/WorkOrderRegister.php.html#15\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderRegister::handle<\/a>"],[0,4,"<a href=\"WorkOrderRegister\/Pipes\/WorkOrderTaskRegister.php.html#13\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderTaskRegister::handle<\/a>"],[0,9,"<a href=\"WorkOrderRegister\/WorkOrderRegisterClient.php.html#33\">App\\Services\\WorkOrderRegister\\WorkOrderRegisterClient::register<\/a>"],[0,3,"<a href=\"WorkOrderRegister\/WorkOrderRegisterClient.php.html#137\">App\\Services\\WorkOrderRegister\\WorkOrderRegisterClient::assignIssueToWorkOrder<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Enums\/CarbonDayOfWeek.php.html#21\">App\\Services\\Scheduling\\Domain\\Enums\\CarbonDayOfWeek::fromString<\/a>"],[0,1,"<a href=\"Scheduling\/Domain\/Enums\/CarbonDayOfWeek.php.html#35\">App\\Services\\Scheduling\\Domain\\Enums\\CarbonDayOfWeek::toString<\/a>"],[0,5,"<a href=\"Scheduling\/Domain\/Traits\/DistanceHelperTrait.php.html#9\">App\\Services\\Scheduling\\Domain\\Traits\\DistanceHelperTrait::calculateStraightLineDistance<\/a>"],[0,1,"<a href=\"Vendor\/Enum\/Service.php.html#17\">App\\Services\\Vendor\\Enum\\Service::getServiceProviderFrom<\/a>"],[0,1,"<a href=\"Vendor\/Enum\/ServiceMode.php.html#10\">App\\Services\\Vendor\\Enum\\ServiceMode::from<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Scope\/WorkOrderScope.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope::all<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Scope\/WorkOrderScope.php.html#28\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope::generateScope<\/a>"],[0,1,"<a href=\"Vendor\/Lula\/Scope\/WorkOrderScope.php.html#34\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope::getPrefix<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
