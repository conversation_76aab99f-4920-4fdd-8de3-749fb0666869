<?php

namespace App\Services;

use App\Models\ProblemCategory;
use App\Models\Role;
use App\Models\Tag;
use App\Models\Technician;
use App\Models\User;
use App\Models\WorkOrder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use UnexpectedValueException;

/**
 * FilterService handles all filtering functionality for the application.
 *
 * This service centralizes filtering logic across the application, providing
 * a consistent way to filter data based on entity type. It maintains a registry
 * of filter handlers that can be applied to query builders.
 *
 * Key features:
 * - Centralized filter handlers for all entity types and fields
 * - Support for complex filtering scenarios (nested filters, different operators)
 * - Integration with Laravel's query builder
 * - Entity ID filtering for specific entity types
 *
 * Usage:
 * ```php
 * // In a controller
 * $query = Model::query();
 * $query = $this->filterService->processQuery($query, $request->all(), 'entity_type');
 *
 * // Or using the Filterable trait on a model
 * $query = Model::query()->process($request->all(), 'entity_type');
 * ```
 *
 * Note: This service handles filtering only. For searching, use the SearchService.
 * For sorting, use the SortService.
 */
class FilterService
{
    /**
     * Registry of filter handlers for different entity types and fields
     * This makes it easy to add new filter types in the future
     *
     * @var array<string, array<string, callable>>
     */
    protected array $filterHandlers = [];

    /**
     * The search service instance.
     */
    protected SearchService $searchService;

    /**
     * The date filter service instance.
     */
    protected DateFilterService $dateFilterService;

    /**
     * The assignee filter service instance.
     */
    protected AssigneeFilterService $assigneeFilterService;

    /**
     * Constructor - initializes the service with default filter handlers and dependencies.
     *
     * When the service is instantiated, it automatically registers default filter handlers
     * for all supported entity types. These handlers define how filtering is applied to query builders.
     *
     * @param  SearchService  $searchService  The search service instance
     * @param  DateFilterService  $dateFilterService  The date filter service instance
     * @param  AssigneeFilterService  $assigneeFilterService  The assignee filter service instance
     */
    public function __construct(SearchService $searchService, DateFilterService $dateFilterService, AssigneeFilterService $assigneeFilterService)
    {
        $this->searchService = $searchService;
        $this->dateFilterService = $dateFilterService;
        $this->assigneeFilterService = $assigneeFilterService;
        $this->registerDefaultFilterHandlers();
    }

    /**
     * Register a filter handler
     *
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user) or '*' for all
     * @param  string  $filterType  The filter type (date, uuid, simple, assignee, etc.)
     * @param  callable  $handler  The handler function
     */
    public function registerFilterHandler(string $entityType, string $filterType, callable $handler): void
    {
        if (! isset($this->filterHandlers[$entityType])) {
            $this->filterHandlers[$entityType] = [];
        }

        $this->filterHandlers[$entityType][$filterType] = $handler;
    }

    /**
     * Get a filter handler
     *
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user)
     * @param  string  $filterType  The filter type (date, uuid, simple, assignee, etc.)
     * @return callable|null The handler function or null if not found
     */
    public function getFilterHandler(string $entityType, string $filterType): ?callable
    {
        // Check for entity-specific handler first
        if (isset($this->filterHandlers[$entityType][$filterType])) {
            return $this->filterHandlers[$entityType][$filterType];
        }

        // Fall back to global handler
        if (isset($this->filterHandlers['*'][$filterType])) {
            return $this->filterHandlers['*'][$filterType];
        }

        return null;
    }

    /**
     * Apply filters to a query builder based on the provided filter expression and entity type.
     *
     * This method applies complex filter conditions to the query builder based on the
     * provided filter expression. The filter expression is a structured array that defines
     * filter groups, operations, fields, and values.
     *
     * Filter expressions support:
     * - Multiple filter groups with AND/OR operations between them
     * - Direct filters (single field/value pairs)
     * - Filter groups (multiple field/value pairs with operations)
     * - Different column operations (is, is_not, contains, etc.)
     *
     * Example filter expression:
     * ```php
     * $filters = [
     *     'fl_group' => [
     *         [
     *             'group_operation' => 'and',
     *             'field' => 'status',
     *             'column_operation' => 'is',
     *             'values' => ['active', 'pending']
     *         ],
     *         [
     *             'group_operation' => 'or',
     *             'filters' => [
     *                 [
     *                     'field' => 'name',
     *                     'column_operation' => 'contains',
     *                     'values' => ['john']
     *                 ],
     *                 [
     *                     'field' => 'email',
     *                     'column_operation' => 'contains',
     *                     'values' => ['example.com']
     *                 ]
     *             ]
     *         ]
     *     ]
     * ];
     * ```
     *
     * @template TModel of Model
     *
     * @param  array<string,mixed>  $filters  The filter expression
     * @param  Builder<TModel>  $queryBuilder  The query builder to apply filters to
     * @param  string  $entityType  The type of entity being filtered (e.g., 'work_order', 'user', 'quote', 'service_request')
     * @return Builder<TModel> The query builder with filters applied
     */
    public function filterQuery(array $filters, Builder $queryBuilder, string $entityType): Builder
    {
        if (! $this->hasFilters($filters)) {
            return $queryBuilder;
        }

        return $queryBuilder->where(function ($query) use ($filters, $entityType) {
            foreach ($filters['fl_group'] as $group) {
                $groupOperation = $group['group_operation'] ?? 'and';

                if ($this->isDirectFilter($group)) {
                    $this->applyDirectFilter($query, $group, $groupOperation, $entityType);
                } elseif ($this->isFilterGroup($group)) {
                    $this->applyFilterGroup($query, $group, $groupOperation, $entityType);
                }
            }
        });
    }

    /**
     * Find the column name for data filtering WRT given filed name.
     */
    public function findFilterColumn(string $fieldName, string $entityType): string
    {
        return match ($entityType) {
            'work_order' => $this->findWorkOrderFilterColumn($fieldName),
            'vendor_work_order' => $this->findVendorWorkOrderFilterColumn($fieldName),
            'service_request' => $this->findServiceRequestFilterColumn($fieldName),
            'user' => $this->findUserFilterColumn($fieldName),
            'quote' => $this->findQuoteFilterColumn($fieldName),
            default => throw new UnexpectedValueException(__('Unexpected entity type [{$entityType}] provided.')),
        };
    }

    /**
     * Generate a where clause for given payload
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    public function generateQuery(array $payload, Builder $query, string $entityType): ?Builder
    {
        // Get the filter type based on the field and entity type
        $filterType = $this->getFilterTypeForField($payload['field'], $entityType);

        // Get the operator for the filter
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], $entityType);

        // Try to get a handler for this filter type
        $handler = $this->getFilterHandler($entityType, $filterType);

        if ($handler) {
            // If we have a handler, use it
            $args = [$query, $payload, $operator, $entityType];

            // Add model parameter for UUID filters
            if ($filterType === 'uuid') {
                $args[] = $this->getModelForField($payload['field'], $entityType);
            }

            return call_user_func_array($handler, $args);
        }

        // Fall back to entity-specific query methods
        return match ($entityType) {
            'work_order' => $this->generateWorkOrderQuery($payload, $query),
            'vendor_work_order' => $this->generateVendorWorkOrderQuery($payload, $query),
            'service_request' => $this->generateServiceRequestQuery($payload, $query),
            'user' => $this->generateUserQuery($payload, $query),
            'quote' => $this->generateQuoteQuery($payload, $query),
            default => throw new UnexpectedValueException(__('Unexpected entity type [{$entityType}] provided.')),
        };
    }

    /**
     * Resolve the where clause operator based on column and operation
     */
    public function resolveWhereClauseOperator(string $column, string $operator, string $entityType): string
    {
        return match ($entityType) {
            'work_order' => $this->resolveWorkOrderWhereClauseOperator($column, $operator),
            'vendor_work_order' => $this->resolveVendorWorkOrderWhereClauseOperator($column, $operator),
            'service_request' => $this->resolveServiceRequestWhereClauseOperator($column, $operator),
            'user' => $this->resolveUserWhereClauseOperator($column, $operator),
            'quote' => $this->resolveQuoteWhereClauseOperator($column, $operator),
            default => throw new UnexpectedValueException(__('Unexpected entity type [{$entityType}] provided.')),
        };
    }

    /**
     * Return a where clause corresponding to the group operation and operation
     */
    public function resolveWhereClause(string $groupOperation, string $whereOperation): string
    {
        $whereClause = null;

        if ($groupOperation === 'and') {
            $whereClause = 'where';
        }

        if ($groupOperation === 'or') {
            $whereClause = 'orWhere';
        }

        $whereClauseOperator = match ($whereOperation) {
            'in' => 'In',
            'date' => 'Date',
            'not_in' => 'NotIn',
            'date_between' => 'Between',
            'uuid' => 'Uuid',
            'not_uuid' => 'NotUuid',
            'date_not_between' => 'NotBetween',
            default => throw new UnexpectedValueException(__("Unexpected where clause operation [{$whereOperation}] provided.")),
        };

        // Building the where clause
        $whereClause .= $whereClauseOperator;

        return $whereClause;
    }

    /**
     * Process a query with filtering based on the provided parameters.
     *
     * This method combines multiple filtering operations into a single method
     * call, making it easy to apply all necessary filters to a query builder. It handles:
     * - Entity ID filtering
     * - Work order ID filtering
     * - Service request ID filtering
     * - Complex filter expressions
     * - Search term filtering (via SearchService)
     *
     * This method centralizes all filtering logic in the FilterService and delegates
     * search functionality to the SearchService.
     *
     * Note: For sorting, use the SortService directly or the Sortable trait on models.
     * For searching, use the SearchService directly or the Searchable trait on models.
     *
     * Example:
     * ```php
     * $query = User::query();
     * $query = $filterService->processQuery($query, $request->all(), 'user');
     * ```
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $queryBuilder  The query builder to process
     * @param  array<string, mixed>  $params  The parameters to apply (search, entity_id, filter, etc.)
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user, etc.)
     * @return Builder<TModel> The processed query builder
     */
    public function processQuery(Builder $queryBuilder, array $params, string $entityType): Builder
    {
        // Apply search if provided (using SearchService)
        if (! empty($params['search'])) {
            $queryBuilder = $this->searchService->applySearch($queryBuilder, $params['search'], $entityType);
        }

        // Apply entity ID filter if provided
        if (! empty($params['entity_id'])) {
            $queryBuilder = $this->applyEntityIdFilter($queryBuilder, $params['entity_id'], $entityType);
        }

        // Apply work_order_id filter if provided
        if (! empty($params['work_order_id'])) {
            $queryBuilder = $this->applyEntityIdFilter($queryBuilder, $params['work_order_id'], $entityType);
        }

        // Apply service_request_id filter if provided
        if (! empty($params['service_request_id'])) {
            $queryBuilder = $this->applyEntityIdFilter($queryBuilder, $params['service_request_id'], $entityType);
        }

        // Apply filters if provided
        if (! empty($params['filter'])) {
            $queryBuilder = $this->filterQuery($params['filter'], $queryBuilder, $entityType);
        }

        return $queryBuilder;
    }

    /**
     * Apply entity ID filter to the query
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $queryBuilder
     * @return Builder<TModel>
     */
    public function applyEntityIdFilter(Builder $queryBuilder, string $uuid, string $entityType): Builder
    {
        return match ($entityType) {
            'work_order' => $queryBuilder->whereUuid($uuid),
            'vendor_work_order' => $queryBuilder->whereUuid($uuid),
            'service_request' => $queryBuilder->whereUuid($uuid),
            'quote' => $queryBuilder->whereUuid($uuid),
            'tag' => $this->applyTagWorkOrderIdFilter($queryBuilder, $uuid),
            default => $queryBuilder->whereUuid($uuid),
        };
    }

    /**
     * Register default filter handlers for all supported entity types and fields.
     *
     * This method sets up the initial filter handlers for common filter types like date,
     * UUID, simple filters, and assignee filters. These handlers are used by the filterQuery
     * method to apply filters to query builders.
     *
     * Each handler is a callable that takes a query builder, filter payload, operator,
     * entity type, and optionally a model, and returns a modified query builder.
     *
     * To add support for a new filter type, add a handler here.
     */
    protected function registerDefaultFilterHandlers(): void
    {
        // Register common filter handlers
        $this->registerFilterHandler('*', 'date', function ($query, $payload, $operator, $entityType) {
            return $this->applyDateFilter($query, $payload, $entityType);
        });

        $this->registerFilterHandler('*', 'uuid', function ($query, $payload, $operator, $entityType, $model) {
            return $this->applyUuidFilter($query, $payload, $model, $entityType);
        });

        $this->registerFilterHandler('*', 'simple', function ($query, $payload, $operator, $entityType) {
            return $this->applySimpleFilter($query, $payload, $entityType);
        });

        $this->registerFilterHandler('*', 'assignee', function ($query, $payload, $operator, $entityType) {
            return $this->assigneeFilterService->applyAssigneeFilter($query, $payload, $operator, $entityType);
        });

        $this->registerFilterHandler('quote', 'submitted_by', function ($query, $payload, $operator) {
            return $this->applySubmittedByFilter($query, $payload, $operator);
        });
    }

    /**
     * Check if the filters array has valid filters
     *
     * @param  array<string,mixed>  $filters
     */
    protected function hasFilters(array $filters): bool
    {
        return isset($filters['fl_group']) && is_array($filters['fl_group']) && count($filters['fl_group']) > 0;
    }

    /**
     * Check if the group is a direct filter
     *
     * @param  array<string,mixed>  $group
     */
    protected function isDirectFilter(array $group): bool
    {
        return isset($group['field']) && isset($group['operation']) && isset($group['values']);
    }

    /**
     * Check if the group is a filter group
     *
     * @param  array<string,mixed>  $group
     */
    protected function isFilterGroup(array $group): bool
    {
        return isset($group['filters']) && is_array($group['filters']);
    }

    /**
     * Apply a direct filter to the query
     *
     * @param  Builder<Model>  $query
     * @param  array<string,mixed>  $filter
     */
    protected function applyDirectFilter(Builder $query, array $filter, string $groupOperation, string $entityType): void
    {
        $columnName = $this->findFilterColumn($filter['field'], $entityType);

        $payload = [
            'group_operation' => $groupOperation,
            'column_operation' => $filter['operation'],
            'field' => $filter['field'],
            'column_name' => $columnName,
            'values' => $filter['values'],
        ];

        $appendQuery = $this->generateQuery($payload, $query, $entityType);

        if (! $appendQuery) {
            throw new UnexpectedValueException(__('Filter query builder failed.'));
        }
    }

    /**
     * Apply a filter group to the query
     *
     * @param  Builder<Model>  $query
     * @param  array<string,mixed>  $group
     */
    protected function applyFilterGroup(Builder $query, array $group, string $groupOperation, string $entityType): void
    {
        $query->where(function ($subQuery) use ($group, $groupOperation, $entityType) {
            foreach ($group['filters'] as $filter) {
                $columnName = $this->findFilterColumn($filter['field'], $entityType);

                $payload = [
                    'group_operation' => $groupOperation,
                    'column_operation' => $filter['operation'],
                    'field' => $filter['field'],
                    'column_name' => $columnName,
                    'values' => $filter['values'],
                ];

                $appendQuery = $this->generateQuery($payload, $subQuery, $entityType);

                if (! $appendQuery) {
                    throw new UnexpectedValueException(__('Filter query builder failed.'));
                }
            }
        });
    }

    /**
     * Find the column name for work order filtering
     */
    protected function findWorkOrderFilterColumn(string $fieldName): string
    {
        return match ($fieldName) {
            'status' => 'work_order_statuses.slug',
            'tag' => 'tag_uuid',
            'technician' => 'technician.technician_uuid',
            'assignee' => 'users.user_uuid',
            'priority' => 'work_orders.priority',
            'due_date' => 'work_orders.due_date',
            'created_date' => 'work_orders.created_at',
            'category' => 'problem_categories.problem_category_uuid',
            'health_score' => 'work_order_health_trackers.classification',
            'provider' => 'work_order_provider_sort',
            default => throw new UnexpectedValueException(__('Unexpected filter column [{$fieldName}] provided.')),
        };
    }

    /**
     * Find the column name for vendor work order filtering
     */
    protected function findVendorWorkOrderFilterColumn(string $fieldName): string
    {
        return match ($fieldName) {
            'status' => 'work_order_statuses.slug',
            'due_date' => 'work_orders.due_date',
            'paused_date' => 'work_orders.paused_at',
            'completed_date' => 'work_orders.completed_at',
            'canceled_date' => 'work_orders.canceled_at',
            'closed_date' => 'closed_at',
            'scheduled_date' => 'scheduled_start_time',
            'category' => 'problem_categories.problem_category_uuid',
            default => throw new UnexpectedValueException(__('Unexpected filter column [{$fieldName}] provided.')),
        };
    }

    /**
     * Find the column name for service request filtering
     */
    protected function findServiceRequestFilterColumn(string $fieldName): string
    {
        return match ($fieldName) {
            'status' => 'service_request_statuses.slug',
            'assignee' => 'users.user_uuid',
            'imported_from' => 'service_request_sources.name',
            'added_date' => 'service_requests.created_at',
            default => throw new UnexpectedValueException(__('Unexpected filter column [{$fieldName}] provided.')),
        };
    }

    /**
     * Find the column name for user filtering
     */
    protected function findUserFilterColumn(string $fieldName): string
    {
        return match ($fieldName) {
            'status' => 'users.status',
            'role' => 'roles.role_uuid',
            default => throw new UnexpectedValueException(__('Unexpected filter column [{$fieldName}] provided.')),
        };
    }

    /**
     * Find the column name for quote filtering
     */
    protected function findQuoteFilterColumn(string $fieldName): string
    {
        return match ($fieldName) {
            'status' => 'quotes.status',
            'tag' => 'tag_uuid',
            'submitted_by' => 'users.user_uuid',
            'assignee' => 'users.user_uuid',
            'submitted_date' => 'quotes.submitted_at',
            'work_order_number' => 'work_orders.work_order_number',
            'category' => 'problem_categories.problem_category_uuid',
            default => throw new UnexpectedValueException(__('Unexpected filter column [{$fieldName}] provided.')),
        };
    }

    /**
     * Generate a where clause for work order payload
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    protected function generateWorkOrderQuery(array $payload, Builder $query): ?Builder
    {
        // Finding the where clause operator for the field and operation from the request
        // ie: the field is 'category' and operation is 'is' then we return the operator 'in';
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], 'work_order');

        // Here we generate the where clause by passing group operation('or'/'and') and the operator(we find it just above code section)
        switch ($payload['field']) {
            case 'tag':
                return $this->applyUuidFilter($query, $payload, new Tag);
            case 'health_score':

                return $query;
            case 'priority':
            case 'status':
                return $this->applySimpleFilter($query, $payload);
            case 'technician':
                return $this->applyUuidFilter($query, $payload, new Technician);
            case 'assignee':
                return $this->assigneeFilterService->applyAssigneeFilter($query, $payload, $operator, 'work_order');
            case 'category':
                return $this->applyUuidFilter($query, $payload, new ProblemCategory);
            case 'due_date':
            case 'created_date':
                return $this->applyDateFilter($query, $payload, 'work_order');
            default:
                throw new UnexpectedValueException("Unexpected filter field [{$payload['field']}] provided.");
        }
    }

    /**
     * Generate a where clause for vendor work order payload
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    protected function generateVendorWorkOrderQuery(array $payload, Builder $query): ?Builder
    {
        // Finding the where clause operator for the field and operation from the request
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], 'vendor_work_order');

        // Here we generate the where clause by passing group operation('or'/'and') and the operator
        switch ($payload['field']) {
            case 'status':
                return $this->applySimpleFilter($query, $payload, 'vendor_work_order');
            case 'category':
                return $this->applyUuidFilter($query, $payload, new ProblemCategory);
            case 'due_date':
            case 'paused_date':
            case 'completed_date':
            case 'canceled_date':
            case 'closed_date':
            case 'scheduled_date':
                return $this->applyDateFilter($query, $payload, 'vendor_work_order');
            default:
                throw new UnexpectedValueException("Unexpected filter field [{$payload['field']}] provided.");
        }
    }

    /**
     * Generate a where clause for service request payload
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    protected function generateServiceRequestQuery(array $payload, Builder $query): ?Builder
    {
        // Finding the where clause operator for the field and operation from the request
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], 'service_request');

        // Here we generate the where clause by passing group operation('or'/'and') and the operator
        switch ($payload['field']) {
            case 'status':
            case 'imported_from':
                return $this->applySimpleFilter($query, $payload, 'service_request');
            case 'assignee':
                return $this->applyAssigneeFilter($query, $payload, $operator, 'service_request');
            case 'added_date':
                return $this->applyDateFilter($query, $payload, 'service_request');
            default:
                throw new UnexpectedValueException("Unexpected filter field [{$payload['field']}] provided.");
        }
    }

    /**
     * Generate a where clause for user payload
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    protected function generateUserQuery(array $payload, Builder $query): ?Builder
    {
        // Finding the where clause operator for the field and operation from the request
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], 'user');

        // Here we generate the where clause by passing group operation('or'/'and') and the operator
        switch ($payload['field']) {
            case 'status':
                return $this->applySimpleFilter($query, $payload, 'user');
            case 'role':
                return $this->applyUuidFilter($query, $payload, new Role);
            default:
                throw new UnexpectedValueException("Unexpected filter field [{$payload['field']}] provided.");
        }
    }

    /**
     * Generate a where clause for quote payload
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    protected function generateQuoteQuery(array $payload, Builder $query): ?Builder
    {
        // Finding the where clause operator for the field and operation from the request
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], 'quote');

        // Here we generate the where clause by passing group operation('or'/'and') and the operator
        switch ($payload['field']) {
            case 'status':
            case 'work_order_number':
                return $this->applySimpleFilter($query, $payload, 'quote');
            case 'submitted_by':
                return $this->applySubmittedByFilter($query, $payload, $operator);
            case 'assignee':
                return $this->applyAssigneeFilter($query, $payload, $operator, 'quote');
            case 'category':
                return $this->applyUuidFilter($query, $payload, new ProblemCategory);
            case 'tag':
                return $this->applyUuidFilter($query, $payload, new Tag);
            case 'submitted_date':
                return $this->applyDateFilter($query, $payload, 'quote');
            default:
                throw new UnexpectedValueException("Unexpected filter field [{$payload['field']}] provided.");
        }
    }

    /**
     * Resolve the where clause operator for work orders
     */
    protected function resolveWorkOrderWhereClauseOperator(string $column, string $operator): string
    {
        return match (true) {
            in_array($column, ['priority', 'status', 'provider', 'health_score']) && $operator === 'is' => 'in',
            in_array($column, ['priority', 'status', 'provider', 'health_score']) && $operator === 'is_not' => 'not_in',
            in_array($column, ['technician', 'assignee', 'category', 'tag']) && $operator === 'is' => 'uuid',
            in_array($column, ['technician', 'assignee', 'category', 'tag']) && $operator === 'is_not' => 'not_uuid',
            in_array($column, ['created_date', 'scheduled_date', 'due_date']) && $operator === 'is_between' => 'date_between',
            in_array($column, ['created_date', 'scheduled_date', 'due_date']) && $operator !== 'is_between' => 'date',
            default => throw new UnexpectedValueException(__('Unexpected where clause column, operator [{$column}, {$operator}] provided.')),
        };
    }

    /**
     * Resolve the where clause operator for vendor work orders
     */
    protected function resolveVendorWorkOrderWhereClauseOperator(string $column, string $operator): string
    {
        return match (true) {
            in_array($column, ['status']) && $operator === 'is' => 'in',
            in_array($column, ['status']) && $operator === 'is_not' => 'not_in',
            in_array($column, ['category']) && $operator === 'is' => 'uuid',
            in_array($column, ['category']) && $operator === 'is_not' => 'not_uuid',
            in_array($column, ['due_date', 'paused_date', 'completed_date', 'canceled_date', 'closed_date', 'scheduled_date']) && $operator === 'is_between' => 'date_between',
            in_array($column, ['due_date', 'paused_date', 'completed_date', 'canceled_date', 'closed_date', 'scheduled_date']) && $operator !== 'is_between' => 'date',
            default => throw new UnexpectedValueException(__('Unexpected where clause column, operator [{$column}, {$operator}] provided.')),
        };
    }

    /**
     * Resolve the where clause operator for service requests
     */
    protected function resolveServiceRequestWhereClauseOperator(string $column, string $operator): string
    {
        return match (true) {
            in_array($column, ['imported_from', 'status']) && $operator === 'is' => 'in',
            in_array($column, ['imported_from', 'status']) && $operator === 'is_not' => 'not_in',
            in_array($column, ['assignee']) && $operator === 'is' => 'uuid',
            in_array($column, ['assignee']) && $operator === 'is_not' => 'not_uuid',
            in_array($column, ['added_date']) && $operator === 'is_between' => 'date_between',
            in_array($column, ['added_date']) && $operator !== 'is_between' => 'date',
            default => throw new UnexpectedValueException(__('Unexpected where clause column, operator [{$column}, {$operator}] provided.')),
        };
    }

    /**
     * Resolve the where clause operator for users
     */
    protected function resolveUserWhereClauseOperator(string $column, string $operator): string
    {
        return match (true) {
            in_array($column, ['status']) && $operator === 'is' => 'in',
            in_array($column, ['status']) && $operator === 'is_not' => 'not_in',
            in_array($column, ['role']) && $operator === 'is' => 'uuid',
            in_array($column, ['role']) && $operator === 'is_not' => 'not_uuid',
            default => throw new UnexpectedValueException(__('Unexpected where clause column, operator [{$column}, {$operator}] provided.')),
        };
    }

    /**
     * Resolve the where clause operator for quotes
     */
    protected function resolveQuoteWhereClauseOperator(string $column, string $operator): string
    {
        return match (true) {
            in_array($column, ['status', 'work_order_number']) && $operator === 'is' => 'in',
            in_array($column, ['status', 'work_order_number']) && $operator === 'is_not' => 'not_in',
            in_array($column, ['submitted_by', 'assignee', 'category', 'tag']) && $operator === 'is' => 'uuid',
            in_array($column, ['assignee', 'category', 'tag']) && $operator === 'is_not' => 'not_uuid',
            in_array($column, ['submitted_date']) && $operator === 'is_between' => 'date_between',
            in_array($column, ['submitted_date']) && $operator !== 'is_between' => 'date',
            default => throw new UnexpectedValueException(__('Unexpected where clause column, operator [{$column}, {$operator}] provided.')),
        };
    }

    /**
     * Apply simple filter to the query (for status, priority, etc.)
     *
     * @param  Builder<Model>  $query
     * @param  array{"group_operation": string, "column_operation": string, "field": string, "column_name": string, "values": array<string>}  $payload
     * @param  string|null  $entityType  The entity type to use for resolving the where clause operator
     * @return Builder<Model>|null
     */
    protected function applySimpleFilter(Builder $query, array $payload, ?string $entityType = null): ?Builder
    {
        $entityType = $entityType ?? 'work_order';
        $whereClause = $this->resolveWhereClause(
            $payload['group_operation'],
            $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], $entityType)
        );

        return $query->$whereClause($payload['column_name'], $payload['values']);
    }

    /**
     * Apply UUID filter to the query
     *
     * @param  Builder<Model>  $query
     * @param  array{"group_operation": string, "column_operation": string, "field": string, "column_name": string, "values": array<string>}  $payload
     * @param  Model  $model  The model to use for UUID lookup
     * @param  string|null  $entityType  The entity type to use for resolving the where clause operator
     * @return Builder<Model>|null
     */
    protected function applyUuidFilter(Builder $query, array $payload, Model $model, ?string $entityType = null): ?Builder
    {
        $entityType = $entityType ?? 'work_order';
        $whereClause = $this->resolveWhereClause(
            $payload['group_operation'],
            $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation'], $entityType)
        );

        return $query->$whereClause($payload['values'], $payload['column_name'], $model);
    }

    /**
     * Apply date filter to the query
     *
     * @param  Builder<Model>  $query
     * @param  array{"group_operation": string, "column_operation": string, "field": string, "column_name": string, "values": array<string>}  $payload
     * @param  string|null  $entityType  The entity type to use for resolving the where clause operator
     * @return Builder<Model>|null
     */
    protected function applyDateFilter(Builder $query, array $payload, ?string $entityType = null): ?Builder
    {
        return $this->dateFilterService->applyDateFilter($query, $payload, $entityType);
    }

    /**
     * Get the filter type for a field
     *
     * @param  string  $field  The field name
     * @param  string  $entityType  The entity type
     * @return string The filter type
     */
    protected function getFilterTypeForField(string $field, string $entityType): string
    {
        // Special cases
        if ($field === 'submitted_by' && $entityType === 'quote') {
            return 'submitted_by';
        }

        if ($field === 'assignee') {
            return 'assignee';
        }

        // Common filter types
        $dateFields = [
            'due_date', 'created_date', 'paused_date', 'completed_date', 'canceled_date',
            'closed_date', 'scheduled_date', 'added_date', 'submitted_date',
        ];

        if (in_array($field, $dateFields)) {
            return 'date';
        }

        $uuidFields = ['tag', 'technician', 'category', 'role'];
        if (in_array($field, $uuidFields)) {
            return 'uuid';
        }

        $simpleFields = ['status', 'priority', 'imported_from', 'work_order_number'];
        if (in_array($field, $simpleFields)) {
            return 'simple';
        }

        // Default
        return $field;
    }

    /**
     * Get the model for a field
     *
     * @param  string  $field  The field name
     * @param  string  $entityType  The entity type
     * @return Model The model
     */
    protected function getModelForField(string $field, string $entityType): Model
    {
        return match ($field) {
            'tag' => new Tag,
            'technician' => new Technician,
            'category' => new ProblemCategory,
            'role' => new Role,
            default => throw new UnexpectedValueException(__('Unexpected field [{$field}] for UUID filter.')),
        };
    }

    /**
     * Apply submitted_by filter to the query
     *
     * @param  Builder<Model>  $query
     * @param  array{"group_operation": string, "column_operation": string, "field": string, "column_name": string, "values": array<string>}  $payload
     * @param  string  $operator  The resolved operator
     * @return Builder<Model>|null
     */
    protected function applySubmittedByFilter(Builder $query, array $payload, string $operator): ?Builder
    {
        $whereClause = $this->resolveWhereClause($payload['group_operation'], $operator);

        // Special handling for 'is' operation with submitted_by
        if (! empty($payload['values']) && $payload['column_operation'] === 'is') {
            $userUuids = $payload['values'];
            $userIds = User::whereUuid($userUuids)->pluck('user_id')->toArray();

            return $query->where(function ($query) use ($userIds) {
                $query->whereIn('quotes.submitted_user_id', $userIds);
            });
        }

        // Default handling
        return $query->$whereClause($payload['values'], $payload['column_name'], new User);
    }

    /**
     * Apply tag work order ID filter
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $queryBuilder
     * @return Builder<TModel>
     */
    protected function applyTagWorkOrderIdFilter(Builder $queryBuilder, string $workOrderUuid): Builder
    {
        $workOrder = WorkOrder::whereUuid($workOrderUuid)->firstOrFail();
        $existingTagIds = DB::table('work_order_tags')
            ->where('work_order_id', $workOrder->work_order_id)
            ->pluck('tag_id');

        if ($existingTagIds->count()) {
            return $queryBuilder->whereNotIn('tag_id', $existingTagIds);
        }

        return $queryBuilder;
    }
}
