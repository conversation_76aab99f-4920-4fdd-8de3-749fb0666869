<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * SearchService handles all search functionality for the application.
 *
 * This service centralizes search logic across the application, providing
 * a consistent way to search data based on entity type. It maintains a registry
 * of search handlers that can be applied to query builders.
 *
 * Key features:
 * - Centralized search handlers for all entity types
 * - Support for complex search scenarios
 * - Integration with Laravel's query builder
 *
 * Usage:
 * ```php
 * // In a controller
 * $query = Model::query();
 * $query = $this->searchService->applySearch($query, $searchTerm, 'entity_type');
 *
 * // Or using the Searchable trait on a model
 * $query = Model::query()->search($searchTerm, 'entity_type');
 * ```
 */
class SearchService
{
    /**
     * Registry of search handlers for different entity types
     *
     * @var array<string, callable>
     */
    protected array $searchHandlers = [];

    /**
     * Constructor - initializes the service with default search handlers.
     */
    public function __construct()
    {
        $this->registerDefaultSearchHandlers();
    }

    /**
     * Register a search handler
     *
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user)
     * @param  callable  $handler  The handler function
     */
    public function registerSearchHandler(string $entityType, callable $handler): void
    {
        $this->searchHandlers[$entityType] = $handler;
    }

    /**
     * Get a search handler
     *
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user)
     * @return callable|null The handler function or null if not found
     */
    public function getSearchHandler(string $entityType): ?callable
    {
        return $this->searchHandlers[$entityType] ?? null;
    }

    /**
     * Apply search to a query builder
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply search to
     * @param  string|null  $searchTerm  The search term to look for
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user)
     * @return Builder<TModel> The query builder with search applied
     */
    public function applySearch(Builder $query, string|null $searchTerm, string $entityType): Builder
    {
        // If search term is null, empty, or only whitespace, return the query as is
        if ($searchTerm === null || trim($searchTerm) === '') {
            return $query;
        }

        // Get the search handler for this entity type
        $handler = $this->getSearchHandler($entityType);

        if ($handler) {
            // If we have a handler, use it
            return call_user_func($handler, $query, $searchTerm);
        }

        // If no handler is found, return the query as is
        return $query;
    }

    /**
     * Register default search handlers for all supported entity types.
     */
    protected function registerDefaultSearchHandlers(): void
    {
        // Work Order search handler
        $this->registerSearchHandler('work_order', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('work_orders.work_order_number', 'like', "%{$searchTerm}%")
                    ->orWhere('work_orders.description', 'like', "%{$searchTerm}%")
                    ->orWhere('properties.full_address', 'like', "%{$searchTerm}%")
                    ->orWhere('problem_categories.label', 'like', "%{$searchTerm}%");
            });
        });

        // Vendor Work Order search handler
        $this->registerSearchHandler('vendor_work_order', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('work_orders.work_order_number', 'like', "%{$searchTerm}%")
                    ->orWhere('properties.full_address', 'like', "%{$searchTerm}%")
                    ->orWhere('problem_categories.label', 'like', "%{$searchTerm}%");
            });
        });

        // Quote search handler
        $this->registerSearchHandler('quote', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('quotes.quote_number', 'like', "%{$searchTerm}%")
                    ->orWhere('quotes.description', 'like', "%{$searchTerm}%")
                    ->orWhere('properties.full_address', 'like', "%{$searchTerm}%")
                    ->orWhere('problem_categories.label', 'like', "%{$searchTerm}%");
            });
        });

        // Service Request search handler
        $this->registerSearchHandler('service_request', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('service_requests.service_request_number', 'like', "%{$searchTerm}%")
                    ->orWhere('service_requests.description', 'like', "%{$searchTerm}%")
                    ->orWhere('properties.full_address', 'like', "%{$searchTerm}%")
                    ->orWhere('problem_categories.label', 'like', "%{$searchTerm}%");
            });
        });

        // User search handler
        $this->registerSearchHandler('user', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('users.first_name', 'like', "%{$searchTerm}%")
                    ->orWhere('users.last_name', 'like', "%{$searchTerm}%")
                    ->orWhere('users.email', 'like', "%{$searchTerm}%");
            });
        });

        // Role search handler
        $this->registerSearchHandler('role', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('roles.name', 'like', "%{$searchTerm}%")
                    ->orWhere('roles.description', 'like', "%{$searchTerm}%");
            });
        });

        // Tag search handler
        $this->registerSearchHandler('tag', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('tags.name', 'like', "%{$searchTerm}%");
            });
        });

        // Vendor search handler
        $this->registerSearchHandler('vendor', function (Builder $query, string $searchTerm) {
            return $query->where(function ($query) use ($searchTerm) {
                $query->where('vendors.name', 'like', "%{$searchTerm}%")
                    ->orWhere('vendors.email', 'like', "%{$searchTerm}%")
                    ->orWhere('vendors.phone', 'like', "%{$searchTerm}%");
            });
        });
    }
}
