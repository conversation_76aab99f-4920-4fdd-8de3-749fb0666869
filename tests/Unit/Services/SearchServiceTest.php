<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Services\SearchService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

/**
 * @group ENG-5636-be-vendor-portal-feature-filters
 */
class SearchServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected SearchService $searchService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->searchService = new SearchService();
    }

    public function testRegisterSearchHandler()
    {
        // Register a custom search handler
        $this->searchService->registerSearchHandler('test_entity', function ($query, $searchTerm) {
            return $query->where('test_column', 'like', "%{$searchTerm}%");
        });

        // Get the registered handler
        $handler = $this->searchService->getSearchHandler('test_entity');

        // Assert that the handler is callable
        $this->assertTrue(is_callable($handler));
    }

    public function testApplySearch()
    {
        // Create a mock query builder
        $query = User::query();

        // Register a test search handler
        $this->searchService->registerSearchHandler('test_entity', function ($query, $searchTerm) {
            return $query->where('test_column', 'like', "%{$searchTerm}%");
        });

        // Apply search
        $result = $this->searchService->applySearch($query, 'test', 'test_entity');

        // Assert that the query has a where clause
        $this->assertStringContainsString(
            'where `test_column` like ?',
            $result->toSql()
        );
    }

    public function testEmptySearchTermReturnsUnmodifiedQuery()
    {
        // Create a mock query builder
        $query = User::query();
        $originalSql = $query->toSql();

        // Apply empty search
        $result = $this->searchService->applySearch($query, '', 'test_entity');

        // Assert that the query is unchanged
        $this->assertEquals($originalSql, $result->toSql());
    }

    public function testDefaultSearchHandlersAreRegistered()
    {
        // Test that the default handlers are registered
        $this->assertNotNull($this->searchService->getSearchHandler('work_order'));
        $this->assertNotNull($this->searchService->getSearchHandler('vendor_work_order'));
        $this->assertNotNull($this->searchService->getSearchHandler('quote'));
        $this->assertNotNull($this->searchService->getSearchHandler('service_request'));
        $this->assertNotNull($this->searchService->getSearchHandler('user'));
        $this->assertNotNull($this->searchService->getSearchHandler('role'));
        $this->assertNotNull($this->searchService->getSearchHandler('tag'));
        $this->assertNotNull($this->searchService->getSearchHandler('vendor'));
    }

    /**
     * @test
     * Test the case where no handler is found for the entity type
     * This covers the unreachable return $query line at the end of applySearch method
     */
    public function testApplySearchWithUnknownEntityTypeReturnsUnmodifiedQuery()
    {
        // Create a mock query builder
        $query = User::query();
        $originalSql = $query->toSql();

        // Apply search with an unknown entity type that has no registered handler
        $result = $this->searchService->applySearch($query, 'test search term', 'unknown_entity_type');

        // Assert that the query is unchanged when no handler is found
        $this->assertEquals($originalSql, $result->toSql());
        $this->assertSame($query, $result, 'Should return the same query instance when no handler is found');
    }

    /**
     * @test
     * Test applySearch with null search term
     */
    public function testApplySearchWithNullSearchTermReturnsUnmodifiedQuery()
    {
        // Create a mock query builder
        $query = User::query();
        $originalSql = $query->toSql();

        // Apply search with null search term
        $result = $this->searchService->applySearch($query, null, 'user');

        // Assert that the query is unchanged
        $this->assertEquals($originalSql, $result->toSql());
    }

    /**
     * @test
     * Test applySearch with whitespace-only search term
     */
    public function testApplySearchWithWhitespaceOnlySearchTermReturnsUnmodifiedQuery()
    {
        // Create a mock query builder
        $query = User::query();
        $originalSql = $query->toSql();

        // Apply search with whitespace-only search term
        $result = $this->searchService->applySearch($query, '   ', 'user');

        // Assert that the query is unchanged (whitespace is considered empty)
        $this->assertEquals($originalSql, $result->toSql());
    }

    /**
     * @test
     * Test applySearch with zero as search term (should not be considered empty)
     */
    public function testApplySearchWithZeroSearchTermAppliesSearch()
    {
        // Create a mock query builder
        $query = User::query();

        // Apply search with '0' as search term (should not be considered empty)
        $result = $this->searchService->applySearch($query, '0', 'user');

        // Assert that the query has been modified (search was applied)
        $this->assertStringContainsString('where', $result->toSql());
        $this->assertStringContainsString('like', $result->toSql());
    }

    /**
     * @test
     * Test getSearchHandler with non-existent entity type
     */
    public function testGetSearchHandlerWithNonExistentEntityTypeReturnsNull()
    {
        // Get handler for non-existent entity type
        $handler = $this->searchService->getSearchHandler('non_existent_entity');

        // Assert that null is returned
        $this->assertNull($handler);
    }

    /**
     * @test
     * Test that registered handlers can be overridden
     */
    public function testRegisterSearchHandlerCanOverrideExistingHandler()
    {
        // Register a custom handler for an existing entity type
        $customHandler = function ($query, $searchTerm) {
            return $query->where('custom_column', 'like', "%{$searchTerm}%");
        };

        $this->searchService->registerSearchHandler('user', $customHandler);

        // Get the handler and verify it's the custom one
        $retrievedHandler = $this->searchService->getSearchHandler('user');
        $this->assertSame($customHandler, $retrievedHandler);

        // Test that the custom handler is used
        $query = User::query();
        $result = $this->searchService->applySearch($query, 'test', 'user');

        $this->assertStringContainsString('where `custom_column` like ?', $result->toSql());
    }

    /**
     * @test
     * Test applySearch with all default entity types to ensure they work
     */
    public function testApplySearchWithAllDefaultEntityTypes()
    {
        $entityTypes = [
            'work_order',
            'vendor_work_order',
            'quote',
            'service_request',
            'user',
            'role',
            'tag',
            'vendor'
        ];

        foreach ($entityTypes as $entityType) {
            $query = User::query(); // Using User model as a base for all tests
            $originalSql = $query->toSql();

            $result = $this->searchService->applySearch($query, 'test search', $entityType);

            // Assert that the query was modified (search was applied)
            $this->assertNotEquals($originalSql, $result->toSql(),
                "Search should be applied for entity type: {$entityType}");

            $this->assertStringContainsString('where', $result->toSql(),
                "Query should contain where clause for entity type: {$entityType}");
        }
    }

    /**
     * @test
     * Test that search handlers receive correct parameters
     */
    public function testSearchHandlerReceivesCorrectParameters()
    {
        $receivedQuery = null;
        $receivedSearchTerm = null;

        // Register a handler that captures the parameters
        $this->searchService->registerSearchHandler('test_entity', function ($query, $searchTerm) use (&$receivedQuery, &$receivedSearchTerm) {
            $receivedQuery = $query;
            $receivedSearchTerm = $searchTerm;
            return $query;
        });

        $query = User::query();
        $searchTerm = 'test search term';

        $this->searchService->applySearch($query, $searchTerm, 'test_entity');

        // Assert that the handler received the correct parameters
        $this->assertSame($query, $receivedQuery);
        $this->assertEquals($searchTerm, $receivedSearchTerm);
    }

    /**
     * @test
     * Test applySearch with special characters in search term
     */
    public function testApplySearchWithSpecialCharactersInSearchTerm()
    {
        $query = User::query();
        $specialSearchTerm = "test's \"quoted\" search & more";

        $result = $this->searchService->applySearch($query, $specialSearchTerm, 'user');

        // Assert that the query was modified and contains the search
        $this->assertStringContainsString('where', $result->toSql());
        $this->assertStringContainsString('like', $result->toSql());
    }

    /**
     * @test
     * Test that handler exceptions are not caught (they should bubble up)
     */
    public function testHandlerExceptionsArePropagated()
    {
        // Register a handler that throws an exception
        $this->searchService->registerSearchHandler('error_entity', function ($query, $searchTerm) {
            throw new \Exception('Handler error');
        });

        $query = User::query();

        // Expect the exception to be thrown
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Handler error');

        $this->searchService->applySearch($query, 'test', 'error_entity');
    }
}
